export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: SearchResultType;
  category: string;
  icon: string;
  route?: string;
  metadata?: any;
  score: number;
  highlightedTitle?: string;
  highlightedDescription?: string;
}

export enum SearchResultType {
  MENU = 'menu',
  SUBMENU = 'submenu',
  FORM = 'form',
  SCREEN = 'screen',
  QUERY = 'query',
  TABLE = 'table',
  FIELD = 'field',
  ACTION = 'action'
}

export interface SearchIndex {
  id: string;
  title: string;
  description: string;
  keywords: string[];
  type: SearchResultType;
  category: string;
  icon: string;
  route?: string;
  metadata: any;
  searchableText: string;
}

export interface SearchOptions {
  maxResults?: number;
  includeTypes?: SearchResultType[];
  excludeTypes?: SearchResultType[];
  minScore?: number;
  fuzzySearch?: boolean;
}

export interface SearchCategory {
  name: string;
  icon: string;
  color: string;
  types: SearchResultType[];
}
