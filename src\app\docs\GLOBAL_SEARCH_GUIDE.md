# Global Search System Documentation

## Overview
The Global Search System provides comprehensive search functionality across all application content including sidebar menus, subscreens, forms, and dynamic content. It features a fixed search textbox in the main toolbar with autocomplete dropdown, keyboard navigation, and intelligent result ranking.

## Features

### 🔍 Search Capabilities
- **Universal Search**: Search across menus, forms, screens, queries, and actions
- **Intelligent Indexing**: Automatically indexes user menus and application content
- **Fuzzy Search**: Supports partial matches and typo tolerance
- **Real-time Results**: Debounced search with instant feedback
- **Categorized Results**: Results organized by type (Navigation, Forms, Queries, Actions)

### ⌨️ Keyboard Navigation
- `↑` / `↓` - Navigate through results
- `Enter` - Select highlighted result
- `Esc` - Close search dropdown
- Auto-focus on search input

### 🎨 User Interface
- **Fixed Position**: Search box always visible in toolbar
- **Responsive Design**: Adapts to mobile and desktop screens
- **Material Design**: Consistent with application theme
- **Highlight Matches**: Search terms highlighted in results
- **Loading States**: Visual feedback during search operations

## Architecture

### Components
1. **GlobalSearchService** (`src/app/services/global-search.service.ts`)
   - Manages search index and operations
   - <PERSON>les debounced search queries
   - Provides search result ranking

2. **GlobalSearchComponent** (`src/app/components/global-search/global-search.component.ts`)
   - Search input and dropdown UI
   - Keyboard navigation handling
   - Result selection and emission

3. **Search Models** (`src/app/core/models/search-result.ts`)
   - TypeScript interfaces for search results
   - Search configuration options
   - Result categorization

### Integration Points
- **HomeComponent**: Hosts the search component in toolbar
- **MetadataService**: Provides menu and form metadata
- **SessionStorageService**: Accesses user profile and menus

## Usage

### Basic Search
1. Click on the search box in the toolbar
2. Type at least 2 characters to start searching
3. Use arrow keys to navigate results
4. Press Enter or click to select a result

### Search Categories
- **Navigation**: Main menus and submenus
- **Forms & Screens**: Dynamic forms and screen components
- **Queries & Reports**: Query builder and report components
- **Fields & Actions**: Form fields and application actions

### Search Operators
- **Exact Match**: Type exact menu or form names
- **Partial Match**: Type part of the name or description
- **Category Filter**: Results automatically categorized by type

## Configuration

### Search Options
```typescript
interface SearchOptions {
  maxResults?: number;        // Default: 50
  includeTypes?: SearchResultType[];
  excludeTypes?: SearchResultType[];
  minScore?: number;         // Default: 0.1
  fuzzySearch?: boolean;     // Default: false
}
```

### Customization
- **Debounce Time**: 300ms (configurable in service)
- **Min Search Length**: 2 characters
- **Max Visible Results**: 8 items in dropdown
- **Search Categories**: Customizable in service

## Styling

### CSS Classes
- `.global-search-container` - Main search wrapper
- `.search-input-wrapper` - Input field container
- `.search-results-container` - Dropdown results
- `.search-result-item` - Individual result item
- `.selected` - Highlighted result state

### Responsive Breakpoints
- **Desktop**: Full search functionality
- **Tablet** (≤768px): Reduced search width
- **Mobile** (≤480px): Compact search interface

## Performance

### Optimization Features
- **Debounced Search**: Prevents excessive API calls
- **Virtual Scrolling**: Limits visible results for performance
- **Lazy Loading**: Submenus loaded on demand
- **Caching**: Search index cached in memory

### Memory Management
- **Index Cleanup**: Automatic cleanup on component destroy
- **Observable Cleanup**: Proper subscription management
- **Event Cleanup**: Keyboard event listeners cleaned up

## Testing

### Unit Tests
- Component initialization and state management
- Keyboard navigation functionality
- Search result filtering and ranking
- Event emission and handling

### Integration Tests
- Search service integration
- Menu selection workflow
- Responsive behavior testing

### Manual Testing Checklist
- [ ] Search input appears in toolbar
- [ ] Typing shows search results
- [ ] Keyboard navigation works
- [ ] Result selection opens correct item
- [ ] Mobile responsive design
- [ ] Loading states display correctly

## Troubleshooting

### Common Issues
1. **No Search Results**
   - Check if user profile has menus
   - Verify search index initialization
   - Check minimum search length (2 chars)

2. **Search Not Appearing**
   - Verify GlobalSearchComponent import
   - Check toolbar CSS styling
   - Ensure component is in imports array

3. **Keyboard Navigation Issues**
   - Check event listener setup
   - Verify preventDefault calls
   - Test focus management

### Debug Tools
- Browser DevTools Console for search index
- Network tab for API calls
- Angular DevTools for component state

## Future Enhancements

### Planned Features
- **Search History**: Recent searches dropdown
- **Advanced Filters**: Filter by date, user, category
- **Search Analytics**: Track popular searches
- **Voice Search**: Speech-to-text integration
- **Search Shortcuts**: Quick access to common items

### API Enhancements
- **Server-side Search**: Offload search to backend
- **Full-text Search**: Search within form content
- **Elasticsearch Integration**: Advanced search capabilities

## API Reference

### GlobalSearchService Methods
```typescript
// Search with options
search(query: string, options?: SearchOptions): Observable<SearchResult[]>

// Get debounced search observable
getSearchObservable(): Observable<SearchResult[]>

// Update search query
updateSearchQuery(query: string): void

// Refresh search index
refreshIndex(): Promise<void>

// Check if index is ready
isIndexReady(): Observable<boolean>
```

### Events
```typescript
// Menu selection event
@Output() menuSelected = new EventEmitter<any>();
```

## Support
For issues or feature requests, please contact the development team or create an issue in the project repository.
