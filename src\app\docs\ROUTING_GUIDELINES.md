# FDIA Routing Guidelines

## Overview
This document outlines best practices for navigation in the FDIA application to ensure no page reloading occurs during navigation.

## ✅ DO - Recommended Navigation Methods

### 1. Use NavigationService (Preferred)
```typescript
// Inject the service
private navigationService = inject(NavigationService);

// Navigate to specific routes
this.navigationService.navigateToLogin();
this.navigationService.navigateToHome();
this.navigationService.navigateToSetPassword();

// Navigate to any route with optional query parameters
this.navigationService.navigateTo('/custom-route', { param1: 'value1' });
```

### 2. Use Angular Router Directly
```typescript
// Inject Router
private router = inject(Router);

// Navigate programmatically
this.router.navigate(['/home']);
this.router.navigate(['/login'], { queryParams: { returnUrl: '/dashboard' } });
```

### 3. Use routerLink in Templates
```html
<!-- For simple navigation -->
<a routerLink="/home">Go to Home</a>

<!-- With parameters -->
<a [routerLink]="['/user', userId]">View User</a>

<!-- With query parameters -->
<a routerLink="/search" [queryParams]="{ q: 'angular' }">Search</a>
```

### 4. Use SafeRoute Directive (Custom)
```html
<!-- Prevents accidental href usage -->
<a appSafeRoute="/home">Go to Home</a>
<a appSafeRoute="/search" [queryParams]="{ q: 'angular' }">Search</a>
```

## ❌ DON'T - Avoid These Methods

### 1. Never Use href for Internal Navigation
```html
<!-- ❌ WRONG - Causes page reload -->
<a href="/home">Go to Home</a>
<a href="#/dashboard">Dashboard</a>
```

### 2. Never Use window.location
```typescript
// ❌ WRONG - Causes page reload
window.location.href = '/home';
window.location.assign('/dashboard');
```

### 3. Never Use document.location
```typescript
// ❌ WRONG - Causes page reload
document.location.href = '/home';
```

## 🔧 Current Implementation Status

### ✅ Already Implemented Correctly
- ✅ Router configuration in `app.routes.ts`
- ✅ Router outlet in `app.component.html`
- ✅ Programmatic navigation in components
- ✅ No href attributes found for internal navigation

### 🆕 New Additions
- ✅ NavigationService for centralized navigation
- ✅ SafeRoute directive for template safety
- ✅ Updated all components to use NavigationService

## 🚀 Best Practices

1. **Always use NavigationService** for consistency
2. **Use routerLink in templates** for declarative navigation
3. **Avoid href attributes** for internal routes
4. **Test navigation** to ensure no page reloads occur
5. **Use Angular DevTools** to monitor router events

## 🧪 Testing Navigation

To verify no page reloading occurs:

1. Open browser DevTools
2. Go to Network tab
3. Navigate through the app
4. Ensure no full page requests are made (only API calls should appear)

## 📝 Code Review Checklist

When reviewing code, check for:
- [ ] No `href` attributes for internal navigation
- [ ] No `window.location` or `document.location` usage
- [ ] Proper use of `routerLink` or `NavigationService`
- [ ] No manual URL manipulation for navigation
