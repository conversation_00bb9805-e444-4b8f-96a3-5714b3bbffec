import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  LoginCredentials,
  LoginResponse,
  UserProfile,
  SetPasswordData,
  AuthError
} from '../core/models/auth.models';
import { SessionStorageService } from './session-storage.service';

@Injectable({
  providedIn: 'root'
})
export class AuthenticationService {
  private readonly http = inject(HttpClient);
  private readonly sessionStorage = inject(SessionStorageService);

  // Centralized API configuration - using environment baseURL
  private readonly API_BASE = environment.baseURL;
  private readonly endpoints = {
    login: `${environment.baseURL}/auth/login`,
    profile: `${environment.baseURL}/api/signon-profile/`, // Keeping your original trailing slash
    logout: `${environment.baseURL}/auth/logout`,
    setPassword: `${environment.baseURL}/auth/set-password`
  } as const;

  private readonly httpOptions = {
    // withCredentials: true // Removed, use token instead
  } as const;

  /**
   * Login user with credentials
   */
  login(credentials: LoginCredentials): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(this.endpoints.login, credentials)
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Get user profile by username - EXACTLY matching your original URL structure
   */
  getProfile(username: string, token?: string): Observable<UserProfile> {
    const headers = token
      ? { headers: { Authorization: `Bearer ${token}` } }
      : {};
    return this.http.get<UserProfile>(`${this.endpoints.profile}${username}`, headers)
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Login and automatically fetch profile - Sugar method for common workflow
   */
  loginWithProfile(credentials: LoginCredentials): Observable<{ response: LoginResponse; profile?: UserProfile }> {
    return this.login(credentials).pipe(
      switchMap(response => {
        if (response.success) {
          if (response['privileges']) {
         this.sessionStorage.setUserPrivileges(response['privileges']);
          this.sessionStorage.setSelectedBranch(response['privileges'][0]);
            }
          // Save languages array from login response
          if (response['languages']) {
            this.saveUserLanguages(response['languages']);
          }
          return this.getProfile(credentials.username).pipe(
            map(profile => ({ response, profile })),
            catchError(error => of({ response, profile: undefined, profileError: error }))
          );
        }
        return of({ response });
      })
    );
  }

  /**
   * Logout user
   */
  logout(): Observable<string> {
    return this.http.post(this.endpoints.logout, {}, {
      responseType: 'text'
    }).pipe(
      tap(() => this.clearLocalStorage()),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Set password for user
   */
  setPassword(data: SetPasswordData): Observable<any> {
    return this.http.post(this.endpoints.setPassword, data)
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Check if user is logged in (sugar method)
   */
  isLoggedIn(): boolean {
    return this.sessionStorage.isLoggedIn();
  }

  /**
   * Get current user profile from sessionStorage (sugar method)
   */
  getCurrentUser(): UserProfile | null {
    return this.sessionStorage.getUserProfile();
  }

  /**
   * Save user profile to sessionStorage (sugar method)
   */
  saveUserProfile(profile: UserProfile): void {
    this.sessionStorage.setUserProfile(profile);
  }

  /**
   * Clear all authentication data (sugar method)
   */
  clearLocalStorage(): void {
    this.sessionStorage.clearAuthData();
  }

  /**
   * Get temporary username from session storage (sugar method)
   */
  getTempUsername(): string | null {
    return this.sessionStorage.getItem('tempUsername');
  }

  /**
   * Check if user can access set password page (sugar method)
   */
  canAccessSetPassword(): boolean {
    return this.sessionStorage.getItem('canAccessSetPassword') === 'true';
  }

  /**
   * Complete login workflow - handles all the common login steps (sugar method)
   */
  completeLogin(credentials: LoginCredentials, onSuccess: (profile: UserProfile) => void, onError: (error: AuthError) => void): void {
    this.loginWithProfile(credentials).subscribe({
      next: ({ response, profile }) => {
        if (response.success && profile) {
          this.saveUserProfile(profile);
          onSuccess(profile);
        } else {
          onError({
            type: 'UNKNOWN',
            message: response.message || 'Login failed'
          });
        }
      },
      error: onError
    });
  }

  /**
   * Save user languages to sessionStorage (new method for language support)
   */
  saveUserLanguages(languages: string[]): void {
    this.sessionStorage.setUserLanguages(languages);
  }

  /**
   * Get user languages from sessionStorage (new method for language support)
   */
  getUserLanguages(): string[] {
    return this.sessionStorage.getUserLanguages();
  }

  /**
   * Check if user has multiple languages (new method for language support)
   */
  hasMultipleLanguages(): boolean {
    const languages = this.getUserLanguages();
    return languages.length > 1;
  }

  /**
   * Enhanced error handling with typed errors
   */
  private handleError(error: HttpErrorResponse): Observable<never> {

    let authError: AuthError;

    if (error.error?.message) {
      const message = error.error.message;

      if (message === 'No password set for this user') {
        authError = {
          type: 'NO_PASSWORD_SET',
          message,
          originalError: error
        };
      } else if (message.includes('Invalid') || message.includes('password')) {
        authError = {
          type: 'INVALID_CREDENTIALS',
          message,
          originalError: error
        };
      } else {
        authError = {
          type: 'UNKNOWN',
          message: `Server Error: ${message}`,
          originalError: error
        };
      }
    } else if (error.status === 0) {
      authError = {
        type: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection and ensure the server is running.',
        originalError: error
      };
    } else if (error.status === 404) {
      authError = {
        type: 'UNKNOWN',
        message: 'Authentication service not found. Please check the API configuration.',
        originalError: error
      };
    } else if (error.status === 500) {
      authError = {
        type: 'UNKNOWN',
        message: 'Server error. Please try again later.',
        originalError: error
      };
    } else {
      authError = {
        type: 'UNKNOWN',
        message: `Unexpected error (${error.status}): ${error.statusText}`,
        originalError: error
      };
    }

    return throwError(() => authError);
  }
}
