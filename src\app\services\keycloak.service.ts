import { Injectable } from '@angular/core';
import Keycloak, { KeycloakInstance, KeycloakInitOptions, KeycloakConfig, KeycloakProfile } from 'keycloak-js';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class KeycloakService {
  private keycloakAuth: KeycloakInstance | undefined;
  private userProfile: KeycloakProfile | null = null;

  // Dynamic config that uses the current host (works for both internal and external access)
  /*private get keycloakConfig(): KeycloakConfig {
    const host = window.location.host; // This includes the port if present
    return {
      url: `${window.location.protocol}//${host}`,  // Uses current host with port dynamically
      realm: 'uff_app_realm',
      clientId: 'uff-browser-deployed'
    };
  }*/
   private get keycloakConfig(): KeycloakConfig {
    return {
      url: environment.keycloak.url,
      realm: environment.keycloak.realm,
      clientId: environment.keycloak.clientId
    };
  }

  /**
   * Initializes Keycloak and returns a promise that resolves when ready
   */
  init(): Promise<boolean> {
    this.keycloakAuth = new Keycloak(this.keycloakConfig);
    const options: KeycloakInitOptions = {
      onLoad: 'login-required',
      checkLoginIframe: false,
      pkceMethod: 'S256' // Enable PKCE with SHA256
    };
    // Add event listeners for token refresh
    this.keycloakAuth.onAuthRefreshSuccess = () => {
    };
    this.keycloakAuth.onAuthRefreshError = () => {
    };
    return this.keycloakAuth.init(options);
  }

  /**
   * Returns true if the user is authenticated
   */
  isLoggedIn(): boolean {
    return !!this.keycloakAuth && !!this.keycloakAuth.authenticated;
  }

  /**
   * Returns the Keycloak token
   */
  getToken(): string | undefined {
    return this.keycloakAuth?.token;
  }

  /**
   * Triggers the Keycloak login flow
   */
  login(): void {
    this.keycloakAuth?.login();
  }

  /**
   * Triggers the Keycloak logout flow
   */
    /**
   * Triggers the Keycloak logout flow
   */
    logout(): void {
      // Use the current host with port dynamically (works for both internal and external access)
      const host = window.location.host; // This includes the port if present
      const postLogoutRedirectUri = `${window.location.protocol}//${host}/`;
      this.keycloakAuth?.logout({ redirectUri: postLogoutRedirectUri });
    }

  /**
   * Loads the Keycloak user profile and caches it
   */
  async loadUserProfile(): Promise<KeycloakProfile | null> {
    if (this.keycloakAuth) {
      try {
        this.userProfile = await this.keycloakAuth.loadUserProfile();
        return this.userProfile;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /**
   * Returns the cached Keycloak profile (if loaded)
   */
  getProfile(): KeycloakProfile | null {
    return this.userProfile;
  }
} 