<div class="set-password-container">
  <!-- Left Side - Animated Background -->
  <div class="left-side">
    <div class="animated-background">
      <!-- Floating geometric elements -->
      <div class="floating-element circle circle-1"></div>
      <div class="floating-element circle circle-2"></div>
      <div class="floating-element circle circle-3"></div>

      <div class="floating-element square square-1"></div>
      <div class="floating-element square square-2"></div>

      <div class="floating-element dot dot-1"></div>
      <div class="floating-element dot dot-2"></div>
      <div class="floating-element dot dot-3"></div>

      <div class="floating-element arrow arrow-1">›</div>
      <div class="floating-element arrow arrow-2">▷</div>

      <div class="floating-element line line-1"></div>
      <div class="floating-element line line-2"></div>

      <div class="floating-element triangle triangle-1">▲</div>
      <div class="floating-element triangle triangle-2">▼</div>

      <div class="floating-element plus plus-1">+</div>
      <div class="floating-element plus plus-2">+</div>
    </div>

    <!-- Welcome Text -->
    <div class="welcome-text">
      <h1 class="hello">Secure</h1>
      <h1 class="welcome">Access!</h1>
    </div>
  </div>

  <!-- Right Side - Set Password Form -->
  <div class="right-side">
    <mat-card class="set-password-card">
      <mat-card-content>
        <!-- Company Logo -->
        <div class="logo-container">
          <img src="assets/images/offical-logo-2.png" alt="Ultimate Solutions Logo" class="logo">
        </div>

        <!-- Page Title -->
        <h2 class="page-title">Set Your Password</h2>

        <!-- Error Message Display -->
        @if (errorMessage) {
          <div class="error-message">
            <mat-icon>error</mat-icon>
            {{ errorMessage }}
          </div>
        }

        <!-- Success Message Display -->
        @if (successMessage) {
          <div class="success-message">
            <mat-icon>check_circle</mat-icon>
            {{ successMessage }}
          </div>
        }

        <!-- Set Password Form -->
        <form [formGroup]="setPasswordForm" (ngSubmit)="onSubmit()" class="set-password-form">
          <!-- New Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>New Password</mat-label>
            <mat-icon matPrefix>lock</mat-icon>
            <input matInput
                   [type]="hideNewPassword ? 'password' : 'text'"
                   formControlName="newPassword"
                   [attr.aria-label]="'New Password'"
                   autocomplete="new-password"
                   placeholder="Enter your new password"
                   class="no-browser-password-toggle">
            <button mat-icon-button
                    matSuffix
                    type="button"
                    (click)="toggleNewPasswordVisibility()"
                    [attr.aria-label]="hideNewPassword ? 'Show password' : 'Hide password'"
                    [attr.aria-pressed]="!hideNewPassword">
              <mat-icon>{{ hideNewPassword ? 'visibility' : 'visibility_off' }}</mat-icon>
            </button>
            @if (setPasswordForm.get('newPassword')?.hasError('required') && setPasswordForm.get('newPassword')?.touched) {
              <mat-error>{{ getErrorMessage('newPassword') }}</mat-error>
            }
            @if (setPasswordForm.get('newPassword')?.hasError('minlength') && setPasswordForm.get('newPassword')?.touched) {
              <mat-error>{{ getErrorMessage('newPassword') }}</mat-error>
            }
          </mat-form-field>

          <!-- Confirm Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Confirm Password</mat-label>
            <mat-icon matPrefix>lock_outline</mat-icon>
            <input matInput
                   [type]="hideConfirmPassword ? 'password' : 'text'"
                   formControlName="confirmPassword"
                   [attr.aria-label]="'Confirm Password'"
                   autocomplete="new-password"
                   placeholder="Confirm your new password"
                   class="no-browser-password-toggle">
            <button mat-icon-button
                    matSuffix
                    type="button"
                    (click)="toggleConfirmPasswordVisibility()"
                    [attr.aria-label]="hideConfirmPassword ? 'Show password' : 'Hide password'"
                    [attr.aria-pressed]="!hideConfirmPassword">
              <mat-icon>{{ hideConfirmPassword ? 'visibility' : 'visibility_off' }}</mat-icon>
            </button>
            @if (setPasswordForm.get('confirmPassword')?.hasError('required') && setPasswordForm.get('confirmPassword')?.touched) {
              <mat-error>{{ getErrorMessage('confirmPassword') }}</mat-error>
            }
            @if (setPasswordForm.hasError('passwordMismatch') && setPasswordForm.get('confirmPassword')?.touched) {
              <mat-error>Passwords do not match</mat-error>
            }
          </mat-form-field>

          <!-- Submit Button -->
          <button mat-raised-button
                  type="submit"
                  class="set-password-button full-width"
                  [disabled]="isLoading || setPasswordForm.invalid">
            @if (isLoading) {
              <mat-spinner diameter="20"></mat-spinner>
            } @else {
              Set Password
            }
          </button>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<!-- Loading Overlay -->
@if (isLoading) {
  <div class="loading-overlay">
    <mat-spinner diameter="40"></mat-spinner>
  </div>
}