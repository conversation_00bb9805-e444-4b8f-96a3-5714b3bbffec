# Lazy Loading Implementation Guide

## Overview

This guide documents the comprehensive lazy loading implementation for all dropdown components in the application. The lazy loading system improves performance by loading dropdown data only when needed, reducing initial page load times and memory usage.

## Features

### 🚀 Core Features
- **On-Demand Loading**: Data loads only when users interact with dropdowns
- **Smart Caching**: Intelligent caching with LRU (Least Recently Used) eviction
- **Performance Monitoring**: Optional performance metrics tracking
- **Configurable Triggers**: Multiple loading triggers (focus, open, search)
- **Backward Compatibility**: Existing dropdowns continue to work without changes

### 📊 Performance Benefits
- **Faster Initial Load**: Reduced initial API calls
- **Memory Efficiency**: Automatic cache cleanup and size limits
- **Network Optimization**: Cached responses reduce redundant requests
- **User Experience**: Better perceived performance with loading indicators

## Configuration

### DropdownConfig Interface

```typescript
export interface DropdownConfig {
  // Existing properties...
  
  // Lazy loading configuration
  enableLazyLoading?: boolean;        // Enable/disable lazy loading (default: true)
  preloadThreshold?: number;          // Preload if expected options < threshold (default: 50)
  cacheTimeout?: number;              // Cache expiration time in ms (default: 300000 = 5 min)
  loadOnFocus?: boolean;              // Load when input gets focus (default: true)
  loadOnOpen?: boolean;               // Load when dropdown opens (default: true)
  loadOnSearch?: boolean;             // Load when user starts searching (default: true)
  maxCacheSize?: number;              // Maximum number of cached entries (default: 100)
  enableCacheCleanup?: boolean;       // Enable automatic cache cleanup (default: true)
  enablePerformanceMonitoring?: boolean; // Enable performance monitoring (default: false)
}
```

### Configuration Examples

#### Basic Lazy Loading
```typescript
const basicConfig: DropdownConfig = {
  type: 'regular',
  queryBuilderId: 'myQuery',
  enableLazyLoading: true,
  loadOnOpen: true
};
```

#### Advanced Configuration
```typescript
const advancedConfig: DropdownConfig = {
  type: 'regular',
  queryBuilderId: 'myQuery',
  enableLazyLoading: true,
  preloadThreshold: 100,
  cacheTimeout: 600000, // 10 minutes
  loadOnFocus: true,
  loadOnOpen: true,
  loadOnSearch: true,
  maxCacheSize: 200,
  enableCacheCleanup: true,
  enablePerformanceMonitoring: true
};
```

#### ID Dropdown (Search-Only Loading)
```typescript
const idConfig: DropdownConfig = {
  type: 'id',
  queryBuilderId: 'idQuery',
  enableLazyLoading: true,
  preloadThreshold: 0,
  loadOnFocus: false,
  loadOnOpen: false,
  loadOnSearch: true, // Only load when user searches
  enablePerformanceMonitoring: true
};
```

## Component Usage

### Regular Field Component
```typescript
getRegularDropdownConfig(field: any): DropdownConfig {
  return {
    type: 'regular',
    queryBuilderId: field.foreginKey,
    searchEnabled: true,
    placeholder: `Search ${field.label?.trim() || field.fieldName}`,
    emptyMessage: 'No options found',
    tooltip: 'Show options',
    enableLazyLoading: true,
    preloadThreshold: 100,
    cacheTimeout: 300000,
    loadOnFocus: true,
    loadOnOpen: true,
    loadOnSearch: true,
    maxCacheSize: 200,
    enableCacheCleanup: true
  };
}
```

### Template Usage
```html
<app-dropdown
  [fieldName]="field.fieldName"
  [formControl]="getFormControl(field.fieldName)"
  [config]="getRegularDropdownConfig(field)"
  [isDisabled]="isViewMode || field.noInput"
  [isReadonly]="isViewMode || field.noInput"
  [fields]="fields"
  [inputId]="getUniqueFieldId(field.fieldName)"
  (valueChange)="onDropdownValueChange($event)">
</app-dropdown>
```

## Lazy Loading Service

### Service Usage
```typescript
import { LazyLoadingService } from '../services/lazy-loading.service';

constructor(private lazyLoadingService: LazyLoadingService) {}

loadOperators(fieldType: string) {
  const endpoint = '/api/operators';
  const payload = { fieldType };
  const cacheKey = `operators_${fieldType}`;
  
  return this.lazyLoadingService.loadData(endpoint, payload, cacheKey, {
    cacheTimeout: 600000,
    maxCacheSize: 20,
    enablePerformanceMonitoring: true
  });
}
```

### Performance Monitoring
```typescript
// Get performance metrics
const metrics = this.lazyLoadingService.getPerformanceMetrics('operators_string');
console.log('Cache hit rate:', metrics?.cacheHitRate);
console.log('Average load time:', metrics?.averageLoadTime);

// Get all metrics
const allMetrics = this.lazyLoadingService.getAllPerformanceMetrics();

// Reset metrics
this.lazyLoadingService.resetPerformanceMetrics('operators_string');
```

## Loading States

### Skeleton Loading
When lazy loading is enabled and no data has been loaded, a skeleton loading state is displayed:

```html
<!-- Skeleton loading for lazy loading -->
@else if (isLazyLoadingEnabled() && !hasDataBeenLoaded && !isLoading) {
  <div class="dropdown-skeleton">
    @for (item of [1,2,3]; track item) {
      <div class="skeleton-item">
        <div class="skeleton-line"></div>
      </div>
    }
    <div class="skeleton-hint">Click to load options...</div>
  </div>
}
```

### Loading Indicators
- **Skeleton Loading**: Shown when lazy loading is enabled but no data loaded
- **Spinner Loading**: Shown during active data loading
- **Dynamic Messages**: Context-aware loading messages

## Cache Management

### Automatic Cleanup
- **LRU Eviction**: Least recently used entries are removed first
- **Size Limits**: Configurable maximum cache size
- **Time-based Expiration**: Automatic cleanup of expired entries
- **Periodic Cleanup**: Background cleanup every 5 minutes

### Manual Cache Control
```typescript
// Clear specific cache
this.lazyLoadingService.clearCache('specific-key');

// Clear all cache
this.lazyLoadingService.clearAllCache();

// Get cache statistics
const stats = this.lazyLoadingService.getCacheStats();
console.log('Cache size:', stats.size);
console.log('Cache keys:', stats.keys);
```

## Best Practices

### 1. Configuration Guidelines
- **ID Dropdowns**: Use search-only loading (`loadOnSearch: true`, others `false`)
- **Type/Foreign Key**: Use moderate preload thresholds (20-30 items)
- **Regular Dropdowns**: Use higher thresholds (100+ items) for better UX
- **Lookup Dropdowns**: Disable lazy loading for preloaded options

### 2. Performance Optimization
- Enable performance monitoring in development
- Monitor cache hit rates and adjust timeouts accordingly
- Use appropriate cache sizes based on data volume
- Consider preload thresholds based on typical dataset sizes

### 3. User Experience
- Provide clear loading indicators
- Use skeleton loading for better perceived performance
- Configure appropriate loading triggers based on user behavior
- Test with realistic network conditions

## Migration Guide

### Existing Dropdowns
Existing dropdowns will continue to work without changes. To enable lazy loading:

1. Update the dropdown configuration:
```typescript
// Before
const config = {
  type: 'regular',
  queryBuilderId: 'myQuery'
};

// After
const config = {
  type: 'regular',
  queryBuilderId: 'myQuery',
  enableLazyLoading: true,
  loadOnOpen: true
};
```

2. Test the dropdown behavior
3. Adjust configuration based on user feedback

### Material Select Components
Material Select components in filter-group and dynamic-query now use the LazyLoadingService for operator loading with automatic caching and performance monitoring.

## Troubleshooting

### Common Issues
1. **Data not loading**: Check network requests and API endpoints
2. **Cache not working**: Verify cache keys are consistent
3. **Performance issues**: Monitor cache hit rates and adjust timeouts
4. **Memory usage**: Check cache sizes and cleanup configuration

### Debug Tools
```typescript
// Enable performance monitoring
config.enablePerformanceMonitoring = true;

// Log performance info
dropdown.logPerformanceInfo();

// Check cache statistics
const stats = lazyLoadingService.getCacheStats();
```

## Testing

Comprehensive tests are included for:
- Lazy loading behavior
- Cache management
- Performance monitoring
- Error handling
- Loading states

Run tests with:
```bash
ng test
```

## Future Enhancements

- **Predictive Loading**: Load data based on user patterns
- **Background Refresh**: Refresh cache in background
- **Compression**: Compress cached data for memory efficiency
- **Analytics**: Advanced performance analytics and reporting
