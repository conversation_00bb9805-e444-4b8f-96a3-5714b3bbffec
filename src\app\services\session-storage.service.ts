import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SessionStorageService {
  
  /**
   * Set item in session storage
   */
  setItem(key: string, value: any): void {
    sessionStorage.setItem(key, JSON.stringify(value));
  }

  /**
   * Get item from session storage
   */
  getItem<T>(key: string): T | null {
    const item = sessionStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  }

  /**
   * Remove item from session storage
   */
  removeItem(key: string): void {
    sessionStorage.removeItem(key);
  }

  /**
   * Check if item exists in session storage
   */
  hasItem(key: string): boolean {
    return sessionStorage.getItem(key) !== null;
  }

  /**
   * Clear all session storage
   */
  clear(): void {
    sessionStorage.clear();
  }

  /**
   * Get user profile from session storage
   */
  getUserProfile(): any {
    return this.getItem('profile');
  }

  /**
   * Set user profile in session storage
   */
  setUserProfile(profile: any): void {
    this.setItem('profile', profile);
  }

  /**
   * Get user privileges from session storage
   */
  getUserPrivileges(): any[] {
    return this.getItem('userPrivileges') || [];
  }

  /**
   * Set user privileges in session storage
   */
  setUserPrivileges(privileges: any[]): void {
    this.setItem('userPrivileges', privileges);
  }

  /**
   * Get selected branch from session storage
   */
  getSelectedBranch(): any {
    return this.getItem('selectedBranch');
  }

  /**
   * Set selected branch in session storage
   */
  setSelectedBranch(branch: any): void {
    this.setItem('selectedBranch', branch);
  }

  /**
   * Get user languages from session storage
   */
  getUserLanguages(): string[] {
    return this.getItem('userLanguages') || ['en'];
  }

  /**
   * Set user languages in session storage
   */
  setUserLanguages(languages: string[]): void {
    this.setItem('userLanguages', languages);
  }

  /**
   * Check if user is logged in
   */
  isLoggedIn(): boolean {
    return this.hasItem('profile');
  }

  /**
   * Clear all authentication data
   */
  clearAuthData(): void {
    this.removeItem('profile');
    this.removeItem('userPrivileges');
    this.removeItem('selectedBranch');
    this.removeItem('userLanguages');
    this.removeItem('canAccessSetPassword');
    this.removeItem('tempUsername');
  }
} 