# Keycloak Integration Guide

## Overview

This guide explains the changes made to integrate Keycloak with session storage instead of local storage, and how to test the integration.

## Changes Made

### 1. **Session Storage Migration**
- **Before**: All data stored in `localStorage`
- **After**: All data stored in `sessionStorage`
- **Benefits**: 
  - Data is cleared when browser tab is closed
  - Better security for sensitive information
  - Automatic cleanup on session end

### 2. **New Services Created**

#### `SessionStorageService`
- Centralized session storage management
- Type-safe methods for storing/retrieving data
- Consistent error handling

#### `KeycloakIntegrationService`
- Handles Keycloak initialization
- Manages user profile and language data
- Provides debugging capabilities

### 3. **Updated Components**

#### `app.config.ts`
- Simplified app initializer using `KeycloakIntegrationService`
- Better error handling and logging
- Automatic language detection from Keycloak profile

#### `AuthenticationService`
- Updated to use `SessionStorageService`
- Consistent session storage operations
- Better language management

#### `HomeComponent`
- Updated to use `SessionStorageService`
- Improved data loading from session storage

## Key Features

### 1. **Language Support**
- Automatically detects user languages from Keycloak profile
- Falls back to backend-provided languages
- Defaults to English if no languages found
- Supports multiple languages for form fields

### 2. **Session Management**
- All user data stored in session storage
- Automatic cleanup on logout
- Persistent across page refreshes within session
- Cleared when browser tab is closed

### 3. **Error Handling**
- Graceful fallbacks for missing data
- Comprehensive logging for debugging
- Default values when services fail

## Testing the Integration

### 1. **Access Debug Page**
Navigate to `/debug` to see:
- Keycloak login status
- Session storage status
- User profile data
- User privileges
- User languages
- Selected branch

### 2. **Test Language Fields**
1. Open a form with language fields
2. Click the globe icon on language fields
3. Verify multiple language inputs appear
4. Test saving and loading language data

### 3. **Test Session Storage**
1. Open browser developer tools
2. Go to Application tab → Session Storage
3. Verify data is stored in session storage
4. Close tab and reopen to verify data is cleared

### 4. **Test Keycloak Integration**
1. Check browser console for initialization logs
2. Verify user profile is loaded
3. Test logout functionality
4. Verify session is cleared on logout

## Debugging

### Console Logs
The integration provides detailed console logs:
```
Starting Keycloak initialization...
Keycloak initialized successfully
Username from token: username
Token available: true
User profile fetched: {...}
User detail fetched: {...}
Languages from Keycloak profile: ["en", "ar"]
Keycloak initialization successful: {...}
```

### Debug Component
Use the debug component at `/debug` to:
- View current user state
- Test session storage operations
- Reinitialize Keycloak if needed
- Clear session data

## Common Issues and Solutions

### 1. **Keycloak Not Initializing**
- Check Keycloak server is running
- Verify environment configuration
- Check browser console for errors

### 2. **Languages Not Loading**
- Verify user has language attributes in Keycloak
- Check backend provides language data
- Default to English if no languages found

### 3. **Session Data Missing**
- Check session storage is enabled
- Verify data is being saved correctly
- Test with debug component

### 4. **Form Language Fields Not Working**
- Verify user has multiple languages
- Check language field component is loaded
- Test with debug component

## Configuration

### Environment Setup
```typescript
// environment.ts
export const environment = {
  baseURL: window.location.origin,
  keycloak: {
    url: `${window.location.protocol}//${window.location.host}`,
    realm: 'uff_app_realm',
    clientId: 'uff-browser-deployed'
  }
};
```

### Keycloak Configuration
- Realm: `uff_app_realm`
- Client: `uff-browser-deployed`
- User attributes should include `locale` for language support

## Benefits of Session Storage

1. **Security**: Data is cleared when browser tab is closed
2. **Privacy**: No persistent data storage
3. **Cleanup**: Automatic session cleanup
4. **Compliance**: Better for data protection regulations
5. **Performance**: Faster access than localStorage

## Migration Notes

- All existing localStorage operations moved to sessionStorage
- Language support enhanced with Keycloak integration
- Better error handling and debugging capabilities
- Improved user experience with automatic language detection

## Next Steps

1. Test the integration thoroughly
2. Monitor console logs for any issues
3. Verify language fields work correctly
4. Test session storage behavior
5. Update any remaining localStorage references 