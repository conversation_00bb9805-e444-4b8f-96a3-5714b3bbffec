import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { DynamicFormComponent } from './dynamic-form.component';

describe('DynamicFormComponent - Grid Groups Support', () => {
  let component: DynamicFormComponent;
  let fixture: any;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DynamicFormComponent],
      imports: [],
      providers: [FormBuilder]
    }).compileComponents();

    fixture = TestBed.createComponent(DynamicFormComponent);
    component = fixture.componentInstance;
  });

  describe('Grid Layout with Groups', () => {
    it('should handle mixed regular and grouped fields in grid layout', () => {
      const fieldsWithGroups = [
        { fieldName: 'regularField1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'groupedField1', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'groupedField2', Group: 'testGroup', row: 2, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'regularField2', row: 2, column: 2, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).distributeFields(fieldsWithGroups, 2);
      
      expect(result.useGrid).toBe(true);
      expect(result.fields.length).toBe(4);
      
      // Check that all fields have grid areas
      result.fields.forEach((field: any) => {
        expect(field.gridArea).toBeDefined();
      });
    });

    it('should handle multi-fields within groups in grid layout', () => {
      const fieldsWithMultiGroups = [
        { fieldName: 'regularField', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'multiGroupField', Group: 'testGroup', isMulti: true, row: 1, column: 2, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).distributeFields(fieldsWithMultiGroups, 2);
      
      expect(result.useGrid).toBe(true);
      expect(result.fields.length).toBe(2);
      
      const multiField = result.fields.find((f: any) => f.fieldName === 'multiGroupField');
      expect(multiField.isMulti).toBe(true);
      expect(multiField.Group).toBe('testGroup');
      expect(multiField.gridArea).toBeDefined();
    });

    it('should handle nested groups in grid layout', () => {
      const fieldsWithNestedGroups = [
        { fieldName: 'parentField', Group: 'parentGroup', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'nestedField', Group: 'parentGroup|childGroup', row: 1, column: 2, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).distributeFields(fieldsWithNestedGroups, 2);
      
      expect(result.useGrid).toBe(true);
      expect(result.fields.length).toBe(2);
      
      const parentField = result.fields.find((f: any) => f.fieldName === 'parentField');
      const nestedField = result.fields.find((f: any) => f.fieldName === 'nestedField');
      
      expect(parentField.Group).toBe('parentGroup');
      expect(nestedField.Group).toBe('parentGroup|childGroup');
      expect(parentField.gridArea).toBeDefined();
      expect(nestedField.gridArea).toBeDefined();
    });

    it('should handle group fields spanning multiple columns', () => {
      const fieldsWithSpanning = [
        { fieldName: 'wideGroupField', Group: 'testGroup', row: 1, column: 1, rowSize: 1, colSize: 2 },
        { fieldName: 'regularField', row: 2, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).distributeFields(fieldsWithSpanning, 2);
      
      expect(result.useGrid).toBe(true);
      
      const wideField = result.fields.find((f: any) => f.fieldName === 'wideGroupField');
      expect(wideField.gridArea).toBe('1 / 1 / 2 / 3'); // Spans 2 columns
    });
  });

  describe('SubScreen Grid Layout with Groups', () => {
    it('should support groups in subscreen grid layout', () => {
      // Mock subscreen metadata with groups
      const subScreenMetadata = {
        ID: 'testSubScreen',
        columnNumber: 2,
        fieldName: [
          { fieldName: 'subField1', row: 1, column: 1, rowSize: 1, colSize: 1 },
          { fieldName: 'subGroupField', Group: 'subGroup', row: 1, column: 2, rowSize: 1, colSize: 1 }
        ]
      };

      // Test that subscreen processes groups correctly
      component.processSubScreen(subScreenMetadata);
      
      expect(component.subScreenUseGrid['testSubScreen']).toBe(true);
      expect(component.subScreenGridFields['testSubScreen'].length).toBe(2);
      
      const groupField = component.subScreenGridFields['testSubScreen'].find((f: any) => f.fieldName === 'subGroupField');
      expect(groupField.Group).toBe('subGroup');
      expect(groupField.gridArea).toBeDefined();
    });
  });

  describe('Grid Layout Fallback', () => {
    it('should fall back to column layout when no positioning data for groups', () => {
      const fieldsWithoutPositioning = [
        { fieldName: 'regularField', type: 'string' },
        { fieldName: 'groupedField', Group: 'testGroup', type: 'string' }
      ];

      const result = (component as any).distributeFields(fieldsWithoutPositioning, 2);
      
      expect(result.useGrid).toBe(false);
      expect(result.columns).toBeDefined();
      expect(result.columns.length).toBe(2);
    });
  });
});
