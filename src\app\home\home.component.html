<div class="home-container">
  <mat-toolbar color="primary" class="compact-toolbar">
    <button mat-icon-button class="menu-icon" (click)="toggleSideMenu()">
      <mat-icon [class.rotated]="isSidenavOpen">menu</mat-icon>
    </button>
    <img src="assets/images/offical-logo-2.png" alt="Company Logo" class="logo" />

    <span class="spacer"></span>
    <div class="icon-frame">
      <img src="assets/images/username.png" alt="Icon" />
    </div>
    <div class="user-info">
      <div class="username"><PERSON><PERSON><PERSON></div>
      <div class="company">My Company Name</div>

    </div>

    
   <!-- to show the Tenant -->
     <!-- <div>
      <span 
      class="privileges clickable" 
      *ngFor="let tenant of userPrivileges"
      (click)="selectBranch(tenant)">
      | Tenant {{ tenant.currentTenant }}
    </span>
    </div> -->



    <!-- <button mat-button class="logout-button" (click)="onLogout()">
      <mat-icon>logout</mat-icon>
      Logout
    </button> -->
  </mat-toolbar>

  <mat-sidenav-container class="side-menu-container compact-sidenav-container">
    <mat-sidenav #sidenav mode="side" class="side-menu compact-sidenav" [opened]="isSidenavOpen || sidebarFilterActive">

      <!-- Global Search Component in Sidebar -->
      <div class="sidebar-search-container">
        <app-global-search (menuSelected)="onSearchMenuSelected($event)"></app-global-search>
      </div>

      <!-- Filter indicator and clear button -->
      @if (sidebarFilterActive) {
        <div class="filter-header">
          <div class="filter-info">
            <mat-icon>filter_list</mat-icon>
            <div class="filter-text">
              <div class="filter-title">Filtered View</div>
              <div class="filter-subtitle">{{ selectedSearchItem?.description || selectedSearchItem?.application }}</div>
            </div>
          </div>
          <button mat-icon-button (click)="clearSidebarFilter()" class="clear-filter-btn" title="Show all items">
            <mat-icon>clear</mat-icon>
          </button>
        </div>
      }

      @for (tab of getDisplayTabs(); track tab) {
        <div class="menu-item">
          <p class="compact-menu-item"
             [attr.data-menu-item]="tab.application"
             (click)="onSidebarMenuItemSelected(tab)">
            <mat-icon class="icon-wrapper">{{ getIcon(tab.type) }}</mat-icon>
            {{ tab.description || tab.application }}
            @if (tab.type === 'menu') {
              <mat-icon class="submenu-arrow" [class.rotated]="expandedSidebarMenus[tab.application]">
                expand_more
              </mat-icon>
            }
          </p>
          @if (tab.type === 'menu' && expandedSidebarMenus[tab.application]) {
            <div class="submenu-item">
              @if (loadingSidebarMenus[tab.application]) {
                <div>Loading...</div>
              }
              @for (submenu of getDisplaySubmenus(tab.application); track submenu) {
                <app-submenu
                  [menuItem]="submenu"
                  [level]="1"
                  (onMenuSelected)="onSidebarMenuItemSelected($event)">
                </app-submenu>
              }
            </div>
          }
        </div>
      }

      <button mat-button class="logout-button" (click)="onLogout()">
        <mat-icon>logout</mat-icon>
        Logout
      </button>
    </mat-sidenav>

    <mat-sidenav-content>
      <div class="tab-container">
        <button
          class="tab-scroll-arrow tab-scroll-left"
          [class.disabled]="!canScrollLeft"
          (click)="scrollTabsLeft()"
          [disabled]="!canScrollLeft">
          <mat-icon>chevron_left</mat-icon>
        </button>

        <div class="tab-scroll-wrapper" #tabScrollWrapper (scroll)="onTabScroll()">
          <ul class="nav nav-tabs" #tabsList>
            @for (tab of openTabs; track tab; let i = $index) {
              <li class="nav-item" [class.active]="i === activeTabIndex">
                <a class="nav-link" (click)="setActiveTab(i)">
                  <mat-icon class="icon-wrapper">{{ getIcon(tab.type) }}</mat-icon>
                  <span class="tab-text">{{ tab.description || tab.application }}</span>
                  <button class="close-tab" (click)="closeTab(i)" [attr.aria-label]="'Close ' + tab.application">&times;</button>
                </a>
              </li>
            }
          </ul>
        </div>

        <button
          class="tab-scroll-arrow tab-scroll-right"
          [class.disabled]="!canScrollRight"
          (click)="scrollTabsRight()"
          [disabled]="!canScrollRight">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>

      <div #tabContent class="tab-content"> </div>
    </mat-sidenav-content>

    
  </mat-sidenav-container>
</div>