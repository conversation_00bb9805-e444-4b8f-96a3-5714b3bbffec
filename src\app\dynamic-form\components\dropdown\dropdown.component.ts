 import { Component, Input, Output, EventEmitter, OnDestroy, OnInit, OnChanges, SimpleChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';
import { KeycloakService } from '../../../services/keycloak.service';

export interface DropdownOption {
  ROW_ID: string;
  [key: string]: any;
}

export interface DropdownConfig {
  type: 'type' | 'foreignKey' | 'regular' | 'id' | 'lookup';
  apiEndpoint?: string;
  queryBuilderId?: string;
  searchEnabled?: boolean;
  placeholder?: string;
  emptyMessage?: string;
  tooltip?: string;
  maxHeight?: string;
  limit?: number;

  // Lazy loading configuration
  enableLazyLoading?: boolean;        // Enable/disable lazy loading (default: false - opt-in)
  preloadThreshold?: number;          // Preload if expected options < threshold (default: 50)
  cacheTimeout?: number;              // Cache expiration time in ms (default: 300000 = 5 min)
  loadOnFocus?: boolean;              // Load when input gets focus (default: true)
  loadOnOpen?: boolean;               // Load when dropdown opens (default: true)
  loadOnSearch?: boolean;             // Load when user starts searching (default: true)
  maxCacheSize?: number;              // Maximum number of cached entries (default: 100)
  enableCacheCleanup?: boolean;       // Enable automatic cache cleanup (default: true)
  enablePerformanceMonitoring?: boolean; // Enable performance monitoring (default: false)
}

export interface DropdownValueChangeEvent {
  fieldName: string;
  value: any;
  option: DropdownOption;
  displayText: string;
}

@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss'
})
export class DropdownComponent implements OnInit, OnDestroy, OnChanges {
  // Core inputs
  @Input() fieldName!: string;
  @Input() formControl!: FormControl;
  @Input() config!: DropdownConfig;
  @Input() isDisabled: boolean = false;
  @Input() isReadonly: boolean = false;
  @Input() options: DropdownOption[] = [];
  @Input() selectedValue: any = '';
  @Input() cssClass: string = '';
  @Input() inputId?: string;

  // Advanced configuration
  @Input() preloadedData: { [key: string]: DropdownOption[] } = {};
  @Input() fields: any[] = []; // For extracting original field names
  @Input() searchDebounceTime: number = 300;
  @Input() showArrowButton: boolean = true;
  @Input() autoClose: boolean = true;

  // Outputs
  @Output() valueChange = new EventEmitter<DropdownValueChangeEvent>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() optionSelect = new EventEmitter<DropdownOption>();

  // Internal state
  showDropdown: boolean = false;
  filteredOptions: DropdownOption[] = [];
  isLoading: boolean = false;
  searchTimeout: any;

  // Performance optimization: API response cache
  private apiCache: { [key: string]: DropdownOption[] } = {};

  // Track when we're setting dropdown values to prevent input conflicts
  private settingDropdownValue: boolean = false;

  // Unique identifier for this dropdown instance
  public uniqueId: string = '';

  // Lazy loading state management
  private cacheTimestamps: { [key: string]: number } = {};
  private cacheAccessTimes: { [key: string]: number } = {};
  public hasDataBeenLoaded: boolean = false;
  private isFirstInteraction: boolean = true;

  private http = inject(HttpClient);
  private keycloakService = inject(KeycloakService);
  private cdr = inject(ChangeDetectorRef);

  private getAuthHeaders() {
    const token = this.keycloakService.getToken();
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`
      })
    };
  }

  ngOnInit() {
    // Generate unique identifier for this dropdown instance
    this.uniqueId = this.inputId || `${this.fieldName}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Initialize with preloaded data if available
    if (this.preloadedData && Object.keys(this.preloadedData).length > 0) {
      this.apiCache = { ...this.preloadedData };
      // Set cache timestamps for preloaded data
      Object.keys(this.preloadedData).forEach(key => {
        this.setCacheTimestamp(key);
      });
    }

    // Set initial filtered options if options are provided (lookup type)
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.hasDataBeenLoaded = true;

      // Update cache for lookup type
      if (this.config.type === 'lookup') {
        const cacheKey = this.getCacheKey();
        this.apiCache[cacheKey] = this.options;
        this.setCacheTimestamp(cacheKey);
      }
    }

    // Always preload by default (original behavior)
    // Only skip preloading if lazy loading is explicitly enabled
    if (this.isLazyLoadingEnabled()) {
      // Lazy loading: only preload if below threshold or explicitly configured
      this.conditionalPreload();
    } else {
      // Default behavior: preload immediately (maintains original performance)
      this.preloadDropdownData();
    }

    // Update disabled state
    this.updateFormControlDisabledState();

    // Setup periodic cache cleanup if lazy loading is enabled
    if (this.isLazyLoadingEnabled() && this.isCacheCleanupEnabled()) {
      // Clean up expired cache entries every 5 minutes
      setInterval(() => {
        this.cleanupExpiredCache();
      }, 300000);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when inputs change
    if (changes['isDisabled'] || changes['isReadonly']) {
      this.updateFormControlDisabledState();
    }

    // Update filtered options when options input changes
    if (changes['options'] && this.options) {
      this.filteredOptions = [...this.options];
      
      // Update cache for lookup type
      if (this.config.type === 'lookup') {
        const cacheKey = this.getCacheKey();
        this.apiCache[cacheKey] = this.options;
      }
    }

    // Update cache when preloaded data changes
    if (changes['preloadedData'] && this.preloadedData) {
      this.apiCache = { ...this.preloadedData };
    }
  }

  ngOnDestroy() {
    // Clear search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  private updateFormControlDisabledState(): void {
    if (this.formControl) {
      if (this.isDisabled || this.isReadonly) {
        if (this.formControl.enabled) {
          this.formControl.disable();
        }
      } else {
        if (this.formControl.disabled) {
          this.formControl.enable();
        }
      }
    }
  }

  toggleDropdown(): void {
    // Prevent interaction when disabled/readonly
    if (this.isDisabled || this.isReadonly) {
      return;
    }

    if (!this.showDropdown) {
      // Lazy loading: load data on dropdown open if enabled and not already loaded
      if (this.isLazyLoadingEnabled() && this.shouldLoadOnOpen() && !this.hasDataBeenLoaded) {
        this.loadDataOnDemand();
      } else {
        const currentValue = this.formControl?.value || '';
        if (currentValue.trim() === '') {
          this.loadAllOptions();
        } else {
          this.searchOptions(currentValue);
        }
      }
    } else {
      this.showDropdown = false;
    }

    this.dropdownToggle.emit(this.showDropdown);
  }

  onInputChange(event: Event): void {
    if (this.settingDropdownValue) {
      return; // Prevent conflicts when setting dropdown values
    }

    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;

    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(searchTerm);
      this.searchChange.emit(searchTerm);
    }, this.searchDebounceTime);
  }

  onInputFocus(): void {
    // Lazy loading: load data on focus if enabled and not already loaded
    if (this.isLazyLoadingEnabled() && this.shouldLoadOnFocus() && this.isFirstInteraction) {
      this.loadDataOnDemand();
      this.isFirstInteraction = false;
    }

    // Auto-open dropdown on focus if not disabled
    if (!this.isDisabled && !this.isReadonly && !this.showDropdown) {
      this.toggleDropdown();
    }
  }

  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    if (this.autoClose) {
      setTimeout(() => {
        this.showDropdown = false;
        this.dropdownToggle.emit(false);
      }, 200);
    }
  }

  selectOption(option: DropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelect.emit(option);
  }

  searchOptions(searchTerm: string): void {
    // Lazy loading: load data on search if enabled and not already loaded
    if (this.isLazyLoadingEnabled() && this.shouldLoadOnSearch() && !this.hasDataBeenLoaded) {
      this.loadDataOnDemand(searchTerm);
      return;
    }

    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // For ID dropdowns, use server-side filtering
    if (this.config.type === 'id') {
      this.loadFromApi(searchTerm);
      return;
    }

    // For other dropdown types, temporarily use client-side filtering
    // until we can debug the server-side filtering properly
    this.loadAllAndFilter(searchTerm);
  }

  loadAllOptions(): void {
    const cacheKey = this.getCacheKey();

    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.showDropdown = true;
      return;
    }

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.updateCacheAccess(cacheKey); // Update access time for LRU
      this.filteredOptions = this.apiCache[cacheKey];
      this.showDropdown = true;
      return;
    }

    // Fallback: Load if not preloaded
    this.loadFromApi();
  }

  private getCacheKey(): string {
    if (this.config.queryBuilderId) {
      return this.config.queryBuilderId;
    }
    
    switch (this.config.type) {
      case 'type':
        return 'fieldType';
      case 'foreignKey':
        return 'formDefinition';
      case 'regular':
        // Extract foreign key from field configuration
        const originalFieldName = this.extractOriginalFieldName(this.fieldName);
        const field = this.fields.find(f => f.fieldName === originalFieldName);
        return field?.foreginKey || 'unknown';
      case 'id':
        return this.config.queryBuilderId || 'id';
      case 'lookup':
        return 'lookup';
      default:
        return 'default';
    }
  }

  private loadFromApi(searchTerm?: string): void {
    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      if (searchTerm && searchTerm.trim() !== '') {
        // Filter lookup options based on search term
        const filtered = this.options.filter(option => {
          return Object.values(option).some(value =>
            value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
          );
        });
        this.filteredOptions = filtered;
      } else {
        this.filteredOptions = [...this.options];
      }
      this.showDropdown = true;
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(searchTerm);

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;

    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;

        if (Array.isArray(response)) {
          // Only cache if no search term (full data)
          if (!searchTerm || searchTerm.trim() === '') {
            const cacheKey = this.getCacheKey();
            this.apiCache[cacheKey] = response;
            this.setCacheTimestamp(cacheKey);

            // Perform cache cleanup after adding new data
            this.cleanupCache();
          }
          this.filteredOptions = response;
          this.showDropdown = true;
          this.hasDataBeenLoaded = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private getApiUrl(): string {
    if (this.config.apiEndpoint) {
      return this.config.apiEndpoint;
    }

    // For lookup type, return empty string as we use preloaded options
    if (this.config.type === 'lookup') {
      return '';
    }

    const queryBuilderId = this.getCacheKey();
    return `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
  }

  private getApiPayload(searchTerm?: string): any {
    const basePayload: any = {};

    // Set the correct _select field based on dropdown type
    if (this.config.type === 'id') {
      basePayload._select = ["ID"];
    } else {
      basePayload._select = ["ID"];
    }

    // Add server-side filtering ONLY for ID dropdowns
    if (searchTerm && searchTerm.trim() !== '' && this.config.type === 'id') {
      basePayload.ID = {
        CT: searchTerm // CT = Contains operator
      };
    }

    // Only add _limit for ID dropdowns since other queryBuilderIds don't support it
    if (this.config.type === 'id') {
      if (this.config.limit) {
        basePayload._limit = this.config.limit;
      } else {
        basePayload._limit = 20;
      }
    }

    return basePayload;
  }

  private loadAllAndFilter(searchTerm: string): void {
    const cacheKey = this.getCacheKey();

    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      const filtered = this.options.filter(option => {
        // For lookup fields, search in label only
        const label = option['label'] || '';
        return label.toLowerCase().includes(searchTerm.toLowerCase());
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option => {
        // For Regular dropdowns, search across all properties
        if (this.config.type === 'regular') {
          return Object.values(option).some(value =>
            value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        // For Type and Foreign Key dropdowns, filter by ROW_ID only
        else {
          return option.ROW_ID && option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load all data from API and then filter client-side
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(); // No search term for client-side filtering

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;
    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        
        if (Array.isArray(response)) {
          // Cache the full response for future client-side filtering
          this.apiCache[cacheKey] = response;
          this.setCacheTimestamp(cacheKey);

          // Filter the response based on search term
          const filtered = response.filter(option => {
            // For Regular dropdowns, search across all properties
            if (this.config.type === 'regular') {
              return Object.values(option).some(value =>
                value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
              );
            }
            // For Type and Foreign Key dropdowns, filter by ROW_ID only
            else {
              return option.ROW_ID && option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase());
            }
          });

          this.filteredOptions = filtered;
          this.showDropdown = true;
          this.hasDataBeenLoaded = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private setEmptyDropdownState(): void {
    this.filteredOptions = [];
    this.showDropdown = true;
  }

  private setDropdownValue(option: DropdownOption): void {
    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue = true;

    if (this.formControl) {
      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);

      // For all dropdown types, store the ID property since API now returns ID for all types
      const storedValue = option['ID'] || option.ROW_ID || '';
      this.formControl.setValue(storedValue);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(this.uniqueId) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Force change detection and validation
      this.formControl.markAsDirty();
      this.formControl.markAsTouched();
      this.formControl.updateValueAndValidity();

      // Force Angular change detection
      this.cdr.detectChanges();
    }

    // Close dropdown
    this.showDropdown = false;

    // Emit value change event with unique identifier
    const displayText = this.getOptionDisplayText(option);
    const storedValue = option['ID'] || option.ROW_ID || '';
    this.valueChange.emit({
      fieldName: this.uniqueId, // Use unique identifier instead of fieldName
      value: storedValue,
      option: option,
      displayText: displayText
    });

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  /**
   * Get the display text for an option (human-readable text to show in input)
   */
  private getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // For ID fields, return the ID property
    if (this.config.type === 'id') {
      return option['ID'] || option.ROW_ID || '';
    }

    // For type fields, return the ROW_ID
    if (this.config.type === 'type') {
      return option.ROW_ID || '';
    }

    // For lookup fields, return the label property
    if (this.config.type === 'lookup') {
      return option['label'] || option.ROW_ID || '';
    }

    // For other foreign key fields, get the first non-ROW_ID property
    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      return option[keys[0]] || option.ROW_ID || '';
    }

    return option.ROW_ID || '';
  }

  /**
   * Extract the original field name from complex field names like:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  private conditionalPreload(): void {
    // For lookup types with provided options, no need to preload
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      return;
    }

    // Check if we should preload based on threshold
    const threshold = this.getPreloadThreshold();
    if (threshold > 0) {
      // For now, we'll preload small datasets (this could be enhanced with metadata)
      // This is a conservative approach for critical dropdowns
      if (this.config.type === 'type' || this.config.type === 'foreignKey') {
        this.preloadDropdownData();
      }
    }
  }

  private loadDataOnDemand(searchTerm?: string): void {
    const cacheKey = this.getCacheKey();

    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.showDropdown = true;
      this.hasDataBeenLoaded = true;
      return;
    }

    // Check if we have valid cached data
    if (this.apiCache[cacheKey] && !this.isCacheExpired(cacheKey)) {
      this.updateCacheAccess(cacheKey); // Update access time for LRU
      if (searchTerm && searchTerm.trim() !== '') {
        this.loadAllAndFilter(searchTerm);
      } else {
        this.filteredOptions = this.apiCache[cacheKey];
        this.showDropdown = true;
      }
      this.hasDataBeenLoaded = true;
      return;
    }

    // Load from API
    this.loadFromApi(searchTerm);
  }

  private preloadDropdownData(): void {
    const cacheKey = this.getCacheKey();

    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      this.apiCache[cacheKey] = this.options;
      this.setCacheTimestamp(cacheKey);
      return;
    }

    // Skip if already cached and not expired
    if (this.apiCache[cacheKey] && !this.isCacheExpired(cacheKey)) {
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload();

    if (!apiUrl) {
      return;
    }

    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.setCacheTimestamp(cacheKey);
          this.hasDataBeenLoaded = true;

          // Perform cache cleanup after adding new data
          this.cleanupCache();
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }

  // Lazy loading configuration helpers
  public isLazyLoadingEnabled(): boolean {
    return this.config.enableLazyLoading === true; // Default to false (opt-in)
  }

  private getPreloadThreshold(): number {
    return this.config.preloadThreshold || 50;
  }

  private getCacheTimeout(): number {
    return this.config.cacheTimeout || 300000; // 5 minutes default
  }

  private shouldLoadOnFocus(): boolean {
    return this.config.loadOnFocus !== false; // Default to true
  }

  private shouldLoadOnOpen(): boolean {
    return this.config.loadOnOpen !== false; // Default to true
  }

  private shouldLoadOnSearch(): boolean {
    return this.config.loadOnSearch !== false; // Default to true
  }

  private isCacheExpired(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps[cacheKey];
    if (!timestamp) return true;
    return Date.now() - timestamp > this.getCacheTimeout();
  }

  private setCacheTimestamp(cacheKey: string): void {
    this.cacheTimestamps[cacheKey] = Date.now();
    this.cacheAccessTimes[cacheKey] = Date.now();
  }

  private updateCacheAccess(cacheKey: string): void {
    this.cacheAccessTimes[cacheKey] = Date.now();
  }

  private getMaxCacheSize(): number {
    return this.config.maxCacheSize || 100;
  }

  private isCacheCleanupEnabled(): boolean {
    return this.config.enableCacheCleanup !== false; // Default to true
  }

  private cleanupCache(): void {
    if (!this.isCacheCleanupEnabled()) return;

    const maxSize = this.getMaxCacheSize();
    const cacheKeys = Object.keys(this.apiCache);

    if (cacheKeys.length <= maxSize) return;

    // Sort by last access time (least recently used first)
    const sortedKeys = cacheKeys.sort((a, b) => {
      const accessTimeA = this.cacheAccessTimes[a] || 0;
      const accessTimeB = this.cacheAccessTimes[b] || 0;
      return accessTimeA - accessTimeB;
    });

    // Remove oldest entries
    const keysToRemove = sortedKeys.slice(0, cacheKeys.length - maxSize);
    keysToRemove.forEach(key => {
      delete this.apiCache[key];
      delete this.cacheTimestamps[key];
      delete this.cacheAccessTimes[key];
    });
  }

  private cleanupExpiredCache(): void {
    const now = Date.now();
    const timeout = this.getCacheTimeout();

    Object.keys(this.cacheTimestamps).forEach(key => {
      if (now - this.cacheTimestamps[key] > timeout) {
        delete this.apiCache[key];
        delete this.cacheTimestamps[key];
        delete this.cacheAccessTimes[key];
      }
    });
  }

  // Loading state helpers
  public getLoadingMessage(): string {
    if (this.isLazyLoadingEnabled() && this.isFirstInteraction) {
      return 'Loading options...';
    }
    return 'Loading...';
  }

  // Performance monitoring helpers
  public getPerformanceMetrics(): any {
    if (this.config.enablePerformanceMonitoring) {
      const cacheKey = this.getCacheKey();
      return {
        cacheKey,
        cacheSize: Object.keys(this.apiCache).length,
        hasData: this.hasDataBeenLoaded,
        isLazyLoading: this.isLazyLoadingEnabled()
      };
    }
    return null;
  }

  public logPerformanceInfo(): void {
    if (this.config.enablePerformanceMonitoring) {
      const metrics = this.getPerformanceMetrics();
      console.log(`[Dropdown Performance] ${this.fieldName}:`, metrics);
    }
  }

  // Utility methods for template
  getKeys(option: DropdownOption): string[] {
    return Object.keys(option);
  }

  // Performance optimization: trackBy functions
  trackByOptionId(_index: number, option: DropdownOption): string {
    // For ID dropdowns, use ID field; for others, use ROW_ID
    if (this.config.type === 'id') {
      return option['ID'] || option.ROW_ID || '';
    }
    return option.ROW_ID || '';
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // Computed properties for template
  get inputClass(): string {
    // Start with base form-input class
    let classes = 'form-input';

    // Add any additional CSS classes passed in
    if (this.cssClass) {
      // If cssClass already contains 'form-input', don't duplicate it
      if (this.cssClass.includes('form-input')) {
        classes = this.cssClass;
      } else {
        classes += ` ${this.cssClass}`;
      }
    }

    // Add disabled state if needed
    if (this.isDisabled || this.isReadonly) {
      classes += ' disabled';
    }

    return classes;
  }

  get dropdownArrowIcon(): string {
    return this.showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  get emptyMessage(): string {
    return this.config.emptyMessage || 'No options found';
  }

  get placeholderText(): string {
    return this.config.placeholder || '';
  }

  get tooltipText(): string {
    return this.config.tooltip || 'Show options';
  }

  get dropdownMaxHeight(): string {
    return this.config.maxHeight || '200px';
  }
}
