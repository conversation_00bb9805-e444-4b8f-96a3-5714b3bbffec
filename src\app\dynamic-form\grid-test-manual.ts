// Manual test for grid positioning functionality
// This file can be run in browser console to test the grid logic

export class GridTestRunner {
  
  // Simulate the grid positioning methods from DynamicFormComponent
  private hasGridPositioning(fields: any[]): boolean {
    if (!fields || !Array.isArray(fields)) {
      return false;
    }
    
    return fields.some(field => 
      field.row !== undefined && 
      field.column !== undefined && 
      field.rowSize !== undefined && 
      field.colSize !== undefined
    );
  }

  private shouldUseGridLayout(fields: any[], columnNumber: number): boolean {
    if (!this.hasGridPositioning(fields)) {
      return false;
    }
    
    return columnNumber > 1;
  }

  private calculateGridArea(field: any): string {
    const row = field.row || 1;
    const column = field.column || 1;
    const rowSize = field.rowSize || 1;
    const colSize = field.colSize || 1;
    
    const rowStart = row;
    const rowEnd = row + rowSize;
    const colStart = column;
    const colEnd = column + colSize;
    
    return `${rowStart} / ${colStart} / ${rowEnd} / ${colEnd}`;
  }

  private resolvePositionConflicts(fields: any[]): any[] {
    const positionMap = new Map<string, any>();
    const resolvedFields: any[] = [];
    
    fields.forEach((field, index) => {
      if (!field.row || !field.column) {
        resolvedFields.push(field);
        return;
      }
      
      const positions: string[] = [];
      for (let r = field.row; r < field.row + (field.rowSize || 1); r++) {
        for (let c = field.column; c < field.column + (field.colSize || 1); c++) {
          positions.push(`${r}-${c}`);
        }
      }
      
      let hasConflict = false;
      for (const pos of positions) {
        if (positionMap.has(pos)) {
          hasConflict = true;
          break;
        }
      }
      
      if (!hasConflict) {
        positions.forEach(pos => positionMap.set(pos, field));
        resolvedFields.push({
          ...field,
          gridArea: this.calculateGridArea(field)
        });
      } else {
        resolvedFields.push({
          ...field,
          row: undefined,
          column: undefined,
          rowSize: undefined,
          colSize: undefined
        });
      }
    });
    
    return resolvedFields;
  }

  // Test the provided metadata example
  public testProvidedExample(): void {

    const testMetadata = {
      columnNumber: 2,
      fieldName: [
        { fieldName: "nickName", type: "string", row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: "fullName", type: "string", mandatory: true, row: 1, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: "gender", type: "string", mandatory: true, row: 2, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: "mobileNo", type: "string", isMulti: true, mandatory: true, row: 2, column: 2, rowSize: 1, colSize: 1 }
      ]
    };

    // Test grid detection
    const hasPositioning = this.hasGridPositioning(testMetadata.fieldName);

    const shouldUseGrid = this.shouldUseGridLayout(testMetadata.fieldName, testMetadata.columnNumber);

    // Test field resolution
    const resolvedFields = this.resolvePositionConflicts(testMetadata.fieldName);

    // Test specific positioning
    const nickNameField = resolvedFields.find(f => f.fieldName === 'nickName');
    const fullNameField = resolvedFields.find(f => f.fieldName === 'fullName');
    const genderField = resolvedFields.find(f => f.fieldName === 'gender');
    const mobileField = resolvedFields.find(f => f.fieldName === 'mobileNo');



    // Expected results
    const expectedResults = {
      nickName: '1 / 1 / 2 / 2',
      fullName: '1 / 2 / 2 / 3',
      gender: '2 / 1 / 3 / 2',
      mobileNo: '2 / 2 / 3 / 3'
    };

    Object.entries(expectedResults).forEach(([fieldName, expected]) => {
      const actual = resolvedFields.find(f => f.fieldName === fieldName)?.gridArea;
      const match = actual === expected;
    });
  }

  // Test conflict resolution
  public testConflictResolution(): void {

    const conflictingFields = [
      { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'field2', row: 1, column: 1, rowSize: 1, colSize: 1 }, // Same position
      { fieldName: 'field3', row: 2, column: 1, rowSize: 1, colSize: 1 }  // Different position
    ];

    const resolved = this.resolvePositionConflicts(conflictingFields);

    const field1 = resolved.find(f => f.fieldName === 'field1');
    const field2 = resolved.find(f => f.fieldName === 'field2');
    const field3 = resolved.find(f => f.fieldName === 'field3');
  }

  // Test spanning fields
  public testSpanningFields(): void {

    const spanningFields = [
      { fieldName: 'header', row: 1, column: 1, rowSize: 1, colSize: 3 }, // Spans 3 columns
      { fieldName: 'sidebar', row: 2, column: 1, rowSize: 3, colSize: 1 }, // Spans 3 rows
      { fieldName: 'content', row: 2, column: 2, rowSize: 2, colSize: 2 }  // Spans 2x2
    ];

    const resolved = this.resolvePositionConflicts(spanningFields);

    resolved.forEach(field => {
    });
  }

  // Run all tests
  public runAllTests(): void {
    this.testProvidedExample();
    this.testConflictResolution();
    this.testSpanningFields();
  }
}

// Export for browser console testing
(window as any).GridTestRunner = GridTestRunner;

// Auto-run tests if in browser
if (typeof window !== 'undefined') {
  const tester = new GridTestRunner();
  tester.runAllTests();
}
