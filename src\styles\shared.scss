// Common form styles
.form-field {
  width: 100%;
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 14px;
    color: #495057;
  }

  input,
  select {
    width: 100%;
    max-width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;

    &:focus {
      border-color: #80bdff;
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// Common button styles
.action-button, .submit-button {
  background-color: #007bff;
  color: #fff;
  font-weight: bold;
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);

  &:hover {
    background-color: #0069d9;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
  }
}

// Common message styles
.error-message {
  background-color: #fdd;
  border: 1px solid #faa;
  color: #a00;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 8px;
}

// Common popup styles
.popup {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 400px;
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  z-index: 100;
}

// Responsive styles
@media (max-width: 768px) {
  .form-field {
    input, select {
      font-size: 16px;
      padding: 10px 12px;
    }
  }
}

@media (max-width: 480px) {
  .form-field {
    input, select {
      padding: 8px 10px;
      font-size: 14px;
    }
  }
} 