import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { SessionStorageService } from '../services/session-storage.service';

export const sessionStorageInterceptor: HttpInterceptorFn = (req, next) => {
  const sessionStorage = inject(SessionStorageService);
  
  // Get all relevant data from session storage
  const userLanguages = sessionStorage.getUserLanguages();
  const userPrivileges = sessionStorage.getUserPrivileges();
  const selectedBranch = sessionStorage.getSelectedBranch();
  const userProfile = sessionStorage.getUserProfile();

  // Build headers object
  const headers: { [key: string]: string } = {};

  // Add languages headers
  if (userLanguages && userLanguages.length > 0) {
    headers['Accept-Language'] = userLanguages.join(',');
    headers['X-User-Languages'] = JSON.stringify(userLanguages);
  }

  // Add privileges header
  if (selectedBranch) {
    headers['privileges'] = JSON.stringify(selectedBranch);
  }

  // Add user profile header if needed
  if (userProfile && userProfile.username) {
    headers['X-User-Profile'] = JSON.stringify({
      username: userProfile.username,
      userId: userProfile.id
    });
  }

  // Add session info header
  headers['X-Session-Info'] = JSON.stringify({
    hasLanguages: userLanguages && userLanguages.length > 0,
    hasPrivileges: userPrivileges && userPrivileges.length > 0,
    isLoggedIn: sessionStorage.isLoggedIn()
  });

  // Clone request with headers
  const modifiedReq = req.clone({
    setHeaders: headers
  });

  return next(modifiedReq);
}; 