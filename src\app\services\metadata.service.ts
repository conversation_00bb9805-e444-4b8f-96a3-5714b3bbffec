import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { KeycloakService } from './keycloak.service';

@Injectable({
  providedIn: 'root'
})
export class MetadataService {
  private menuUrl = `${environment.baseURL}/api/menu/`; // Base URL for menu API
  private tableMetadataUrl = `${environment.baseURL}/api/merged/`; // Base URL for table metadata
  private screenMetadataUrl = `${environment.baseURL}/api/merged/`; // Base URL for table metadata
  private http = inject(HttpClient);

  constructor(private keycloakService: KeycloakService) { }

  private getAuthHeaders(): { headers: HttpHeaders } {
    const token = this.keycloakService.getToken();
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`
      })
    };
  }

  getMenu(application: string): Observable<any> {
    return this.http.get(`${this.menuUrl}${application}`, this.getAuthHeaders());
  }
  getTableMetadata(tableName: string): Observable<any> {
    return this.http.get(`${this.tableMetadataUrl}${tableName}/metadata`, this.getAuthHeaders());
  }
  getScreenMetadata(tableName: string): Observable<any> {
    return this.http.get(`${this.screenMetadataUrl}${tableName}/metadata`, this.getAuthHeaders());
  }
  getScreen(screenId: string): Observable<any> {
    // TODO: Implement screen fetching logic
    return new Observable(); 
  }
  getCriteriaFields(queryName: string): Observable<any[]> {
    return this.http.get<any[]>(`${environment.baseURL}/api/query-builder/${queryName}`, this.getAuthHeaders());
  }
}
