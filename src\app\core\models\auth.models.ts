// Authentication related interfaces and types
// Consolidated from login.interface.ts and authentication.service.ts

export interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message?: string;
  token?: string;
  // Making this flexible to match your actual API response
  [key: string]: any;
}

export interface UserProfile {
  // Making this flexible to match your actual API response
  [key: string]: any;
}

export interface SetPasswordData {
  username: string;
  password: string;
  confirmPassword: string;
}

export interface AuthError {
  type: 'INVALID_CREDENTIALS' | 'NO_PASSWORD_SET' | 'NETWORK_ERROR' | 'UNKNOWN';
  message: string;
  originalError?: any;
}

export interface LoginState {
  isLoading: boolean;
  errorMessage: string;
  hidePassword: boolean;
}
