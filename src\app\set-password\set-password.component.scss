// Global scroll behavior for smooth scrolling
:host {
  scroll-behavior: smooth;
}

// Ensure proper scrolling on mobile devices
* {
  -webkit-overflow-scrolling: touch;
}

// Fully responsive split-screen set-password container with scroll support
.set-password-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  min-height: 100vh;
  height: auto;
  overflow: visible;
  position: relative;
  scroll-behavior: smooth;

  // Large screens (1200px+)
  @media (min-width: 1200px) {
    grid-template-columns: 3fr 2fr;
    height: 100vh;
    overflow: hidden;
  }

  // Medium screens (769px - 1199px)
  @media (max-width: 1199px) and (min-width: 769px) {
    grid-template-columns: 1fr 1fr;
    height: 100vh;
    overflow: hidden;
  }

  // Tablets (481px - 768px)
  @media (max-width: 768px) and (min-width: 481px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    height: auto;
    min-height: 100vh;
    overflow-y: auto;
  }

  // Mobile (361px - 480px)
  @media (max-width: 480px) and (min-width: 361px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    height: auto;
    min-height: 100vh;
    overflow-y: auto;
  }

  // Professional subtle pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(30, 64, 175, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
}

// Left side - Responsive with content visibility support
.left-side {
  position: relative;
  background: linear-gradient(135deg,
    #1e3a8a 0%,
    #1e40af 50%,
    #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 2rem;

  // Large screens - Fixed height
  @media (min-width: 1200px) {
    padding: 2.5rem;
    min-height: 100vh;
  }

  // Medium screens - Fixed height
  @media (max-width: 1199px) and (min-width: 769px) {
    padding: 2rem;
    min-height: 100vh;
  }

  // Tablets - Auto height with minimum
  @media (max-width: 768px) and (min-width: 481px) {
    min-height: 50vh;
    padding: 1.5rem;
    overflow: visible;
  }

  // Mobile - Auto height with minimum
  @media (max-width: 480px) and (min-width: 361px) {
    min-height: 45vh;
    padding: 1rem;
    overflow: visible;
  }

  // Small mobile - Auto height with minimum
  @media (max-width: 360px) {
    min-height: 40vh;
    padding: 0.75rem;
    overflow: visible;
  }

  // Professional overlay
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  // Minimal professional background elements
  .animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;

    // Much more obvious floating elements with enhanced visibility
    .floating-element {
      position: absolute;
      opacity: 0.6; // Significantly increased opacity for maximum visibility
      animation-iteration-count: infinite;
      animation-timing-function: ease-in-out;
      transition: all 0.3s ease;
      cursor: pointer;

      // Interactive hover effects for desktop
      @media (hover: hover) {
        &:hover {
          opacity: 0.9 !important;
          transform: scale(1.4) !important;
          z-index: 10;
          animation-play-state: paused;
        }
      }

      // Much more obvious circles with strong glow effect
      &.circle {
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 40px rgba(255, 255, 255, 0.3), 0 0 80px rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        animation-name: secureFloat;
        animation-duration: 8s;

        &.circle-1 {
          width: 100px;
          height: 100px;
          top: 12%;
          left: 8%;
          animation-delay: 0s;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.4), rgba(59, 130, 246, 0.6));
          box-shadow: 0 0 50px rgba(59, 130, 246, 0.4), 0 0 100px rgba(59, 130, 246, 0.2);
        }

        &.circle-2 {
          width: 75px;
          height: 75px;
          top: 62%;
          right: 12%;
          animation-delay: 3s;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.35), rgba(34, 197, 94, 0.5));
          box-shadow: 0 0 45px rgba(34, 197, 94, 0.4), 0 0 90px rgba(34, 197, 94, 0.2);
        }

        &.circle-3 {
          width: 120px;
          height: 120px;
          bottom: 15%;
          left: 15%;
          animation-delay: 6s;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.38), rgba(168, 85, 247, 0.55));
          box-shadow: 0 0 55px rgba(168, 85, 247, 0.4), 0 0 110px rgba(168, 85, 247, 0.2);
        }
      }

      // Much more obvious squares with strong borders and glow
      &.square {
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(59, 130, 246, 0.4));
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.2), 0 0 60px rgba(59, 130, 246, 0.15);
        animation-name: secureRotateScale;
        animation-duration: 12s;

        &.square-1 {
          width: 50px;
          height: 50px;
          top: 28%;
          right: 10%;
          animation-delay: 2s;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(34, 197, 94, 0.4));
          box-shadow: 0 0 35px rgba(34, 197, 94, 0.3);
        }

        &.square-2 {
          width: 40px;
          height: 40px;
          bottom: 32%;
          right: 25%;
          animation-delay: 8s;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(168, 85, 247, 0.4));
          box-shadow: 0 0 30px rgba(168, 85, 247, 0.3);
        }
      }

      // Much more obvious dots with strong glow
      &.dot {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 25px rgba(255, 255, 255, 0.4), 0 0 50px rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        animation-name: secureBreathe;
        animation-duration: 4s;

        &.dot-1 {
          top: 20%;
          right: 20%;
          animation-delay: 1s;
          background: radial-gradient(circle, rgba(34, 197, 94, 0.7), rgba(255, 255, 255, 0.5));
          box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
        }

        &.dot-2 {
          bottom: 25%;
          left: 35%;
          animation-delay: 5s;
          background: radial-gradient(circle, rgba(59, 130, 246, 0.7), rgba(255, 255, 255, 0.5));
          box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
        }

        &.dot-3 {
          top: 70%;
          left: 55%;
          animation-delay: 9s;
          background: radial-gradient(circle, rgba(168, 85, 247, 0.7), rgba(255, 255, 255, 0.5));
          box-shadow: 0 0 30px rgba(168, 85, 247, 0.5);
        }
      }

      // Much more obvious arrows with strong glow
      &.arrow {
        color: rgba(255, 255, 255, 0.7);
        font-size: 36px;
        font-weight: bold;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.5), 0 0 40px rgba(255, 255, 255, 0.3);
        animation-name: secureSlide;
        animation-duration: 10s;

        &.arrow-1 {
          top: 40%;
          left: 15%;
          animation-delay: 4s;
          color: rgba(34, 197, 94, 0.8);
          text-shadow: 0 0 25px rgba(34, 197, 94, 0.6);
        }

        &.arrow-2 {
          bottom: 30%;
          right: 20%;
          animation-delay: 7s;
          transform: rotate(180deg);
          color: rgba(59, 130, 246, 0.8);
          text-shadow: 0 0 25px rgba(59, 130, 246, 0.6);
        }
      }

      // Much more obvious lines with strong glow
      &.line {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(59, 130, 246, 0.5), rgba(255, 255, 255, 0.3));
        border-radius: 3px;
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), 0 0 40px rgba(59, 130, 246, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation-name: secureWave;
        animation-duration: 6s;

        &.line-1 {
          width: 5px;
          height: 90px;
          top: 25%;
          left: 30%;
          animation-delay: 3s;
          background: linear-gradient(180deg, rgba(34, 197, 94, 0.5), rgba(255, 255, 255, 0.3));
          box-shadow: 0 0 25px rgba(34, 197, 94, 0.4);
        }

        &.line-2 {
          width: 110px;
          height: 5px;
          bottom: 40%;
          right: 15%;
          animation-delay: 9s;
          background: linear-gradient(90deg, rgba(168, 85, 247, 0.5), rgba(255, 255, 255, 0.3));
          box-shadow: 0 0 25px rgba(168, 85, 247, 0.4);
        }
      }

      // Much more obvious triangles with strong glow
      &.triangle {
        color: rgba(255, 255, 255, 0.6);
        font-size: 32px;
        font-weight: bold;
        text-shadow: 0 0 25px rgba(255, 255, 255, 0.4), 0 0 50px rgba(255, 255, 255, 0.2);
        animation-name: secureFlip;
        animation-duration: 14s;

        &.triangle-1 {
          top: 50%;
          right: 30%;
          animation-delay: 2s;
          color: rgba(59, 130, 246, 0.8);
          text-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
        }

        &.triangle-2 {
          bottom: 45%;
          left: 25%;
          animation-delay: 10s;
          color: rgba(168, 85, 247, 0.8);
          text-shadow: 0 0 30px rgba(168, 85, 247, 0.6);
        }
      }

      // Much more obvious plus signs with strong glow
      &.plus {
        color: rgba(255, 255, 255, 0.65);
        font-size: 30px;
        font-weight: bold;
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.5), 0 0 60px rgba(255, 255, 255, 0.3);
        animation-name: secureCrossRotate;
        animation-duration: 16s;

        &.plus-1 {
          top: 35%;
          left: 40%;
          animation-delay: 1s;
          color: rgba(34, 197, 94, 0.8);
          text-shadow: 0 0 35px rgba(34, 197, 94, 0.6);
        }

        &.plus-2 {
          bottom: 55%;
          right: 35%;
          animation-delay: 11s;
          color: rgba(168, 85, 247, 0.8);
          text-shadow: 0 0 35px rgba(168, 85, 247, 0.6);
        }
      }
    }
  }

  // Welcome text styling
  .welcome-text {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;

    .hello, .welcome {
      font-size: 3.5rem;
      font-weight: 300;
      margin: 0;
      line-height: 1.1;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      animation: fadeInUp 1s ease-out;

      // Large screens
      @media (min-width: 1200px) {
        font-size: 4rem;
      }

      // Medium screens
      @media (max-width: 1199px) and (min-width: 769px) {
        font-size: 3rem;
      }

      // Tablets
      @media (max-width: 768px) {
        font-size: 2.5rem;
      }

      // Mobile
      @media (max-width: 480px) {
        font-size: 2rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        font-size: 1.75rem;
      }
    }

    .hello {
      animation-delay: 0.2s;
    }

    .welcome {
      font-weight: 600;
      animation-delay: 0.4s;
    }
  }
}

// Right side - Responsive form container with scroll support
.right-side {
  position: relative;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  z-index: 1;

  // Large screens - Fixed height
  @media (min-width: 1200px) {
    padding: 2.5rem;
    min-height: 100vh;
    overflow-y: auto;
  }

  // Medium screens - Fixed height
  @media (max-width: 1199px) and (min-width: 769px) {
    padding: 2rem;
    min-height: 100vh;
    overflow-y: auto;
  }

  // Tablets - Auto height with scroll support
  @media (max-width: 768px) and (min-width: 481px) {
    min-height: 50vh;
    padding: 1.5rem;
    overflow-y: auto;
  }

  // Mobile - Auto height with scroll support
  @media (max-width: 480px) and (min-width: 361px) {
    min-height: 55vh;
    padding: 1rem;
    overflow-y: auto;
  }

  // Small mobile - Auto height with scroll support
  @media (max-width: 360px) {
    min-height: 60vh;
    padding: 0.75rem;
    overflow-y: auto;
  }

  // Scroll indicator for mobile devices
  @media (max-width: 768px) {
    &::after {
      content: '';
      position: fixed;
      bottom: 10px;
      right: 10px;
      width: 4px;
      height: 40px;
      background: rgba(30, 64, 175, 0.3);
      border-radius: 2px;
      z-index: 1000;
      opacity: 0;
      animation: scrollHint 3s ease-in-out 1s infinite;
    }
  }

  .set-password-card {
    width: 100%;
    max-width: 420px;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(30, 58, 138, 0.12),
      0 4px 16px rgba(30, 64, 175, 0.08) !important;
    border-radius: 16px !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease-out;
    position: relative;
    z-index: 1;
    margin: auto;
    min-height: auto;

    // Large screens - Centered with fixed size
    @media (min-width: 1200px) {
      max-width: 450px;
      padding: 3rem;
    }

    // Medium screens - Centered with fixed size
    @media (max-width: 1199px) and (min-width: 769px) {
      max-width: 380px;
      padding: 2rem;
    }

    // Tablets - Full width with scroll support
    @media (max-width: 768px) and (min-width: 481px) {
      max-width: 100%;
      padding: 1.5rem;
      margin: 0;
      border-radius: 12px !important;
      min-height: auto;
    }

    // Mobile - Full width with scroll support
    @media (max-width: 480px) and (min-width: 361px) {
      max-width: 100%;
      padding: 1.25rem;
      border-radius: 8px !important;
      box-shadow:
        0 4px 16px rgba(30, 58, 138, 0.08),
        0 2px 8px rgba(30, 64, 175, 0.06) !important;
      margin: 0;
      min-height: auto;
    }

    // Small mobile - Full width with scroll support
    @media (max-width: 360px) {
      max-width: 100%;
      padding: 1rem;
      border-radius: 6px !important;
      box-shadow: 0 2px 8px rgba(30, 58, 138, 0.06) !important;
      margin: 0;
      min-height: auto;
    }

    // Simplified responsive company logo styling
    .logo-container {
      text-align: center;
      margin-bottom: 2rem;

      // Tablets
      @media (max-width: 768px) {
        margin-bottom: 1.5rem;
      }

      // Mobile
      @media (max-width: 480px) {
        margin-bottom: 1rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        margin-bottom: 0.75rem;
      }

      .logo {
        width: 160px;
        height: auto;
        max-width: 100%;
        object-fit: contain;
        animation: fadeInScale 0.8s ease-out;
        transition: opacity 0.3s ease;

        // Large screens
        @media (min-width: 1200px) {
          width: 180px;
        }

        // Medium screens
        @media (max-width: 1199px) and (min-width: 769px) {
          width: 140px;
        }

        // Tablets
        @media (max-width: 768px) {
          width: 120px;
        }

        // Mobile
        @media (max-width: 480px) {
          width: 100px;
        }

        // Small mobile
        @media (max-width: 360px) {
          width: 90px;
        }

        &:hover {
          opacity: 0.9;
        }
      }
    }

    // Page title styling
    .page-title {
      text-align: center;
      margin-bottom: 2rem;
      color: #1e40af;
      font-size: 1.75rem;
      font-weight: 600;
      animation: fadeInUp 0.8s ease-out 0.2s both;

      // Large screens
      @media (min-width: 1200px) {
        font-size: 2rem;
      }

      // Medium screens
      @media (max-width: 1199px) and (min-width: 769px) {
        font-size: 1.5rem;
      }

      // Tablets
      @media (max-width: 768px) {
        margin-bottom: 1.5rem;
        font-size: 1.4rem;
      }

      // Mobile
      @media (max-width: 480px) {
        margin-bottom: 1rem;
        font-size: 1.25rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        margin-bottom: 0.75rem;
        font-size: 1.1rem;
      }
    }

    // Error message styling
    .error-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      margin-bottom: 1.5rem;
      background-color: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.2);
      border-radius: 8px;
      color: #dc2626;
      font-size: 0.875rem;
      animation: fadeInUp 0.4s ease-out;

      // Tablets
      @media (max-width: 768px) {
        margin-bottom: 1.25rem;
        padding: 0.625rem 0.875rem;
      }

      // Mobile
      @media (max-width: 480px) {
        margin-bottom: 1rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        margin-bottom: 0.75rem;
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
      }

      mat-icon {
        font-size: 1.125rem;
        width: 1.125rem;
        height: 1.125rem;
      }
    }

    // Success message styling
    .success-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      margin-bottom: 1.5rem;
      background-color: rgba(34, 197, 94, 0.1);
      border: 1px solid rgba(34, 197, 94, 0.2);
      border-radius: 8px;
      color: #16a34a;
      font-size: 0.875rem;
      animation: fadeInUp 0.4s ease-out;

      // Tablets
      @media (max-width: 768px) {
        margin-bottom: 1.25rem;
        padding: 0.625rem 0.875rem;
      }

      // Mobile
      @media (max-width: 480px) {
        margin-bottom: 1rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        margin-bottom: 0.75rem;
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
      }

      mat-icon {
        font-size: 1.125rem;
        width: 1.125rem;
        height: 1.125rem;
      }
    }

    // Simplified responsive set-password form styling
    .set-password-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      // Tablets
      @media (max-width: 768px) {
        gap: 1.25rem;
      }

      // Mobile
      @media (max-width: 480px) {
        gap: 1rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        gap: 0.75rem;
      }

      .full-width {
        width: 100%;
      }

      // Angular Material button with custom theming
      .set-password-button {
        width: 100%;
        margin-top: 1.5rem;
        min-height: 48px;
        font-size: 1rem;
        font-weight: 500;
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%) !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 4px 14px rgba(30, 58, 138, 0.3) !important;

        // Large screens
        @media (min-width: 1200px) {
          font-size: 1.1rem;
          min-height: 52px;
        }

        // Medium screens
        @media (max-width: 1199px) and (min-width: 769px) {
          font-size: 1rem;
          min-height: 48px;
        }

        // Tablets
        @media (max-width: 768px) {
          margin-top: 1.25rem;
          min-height: 44px;
          font-size: 0.95rem;
        }

        // Mobile
        @media (max-width: 480px) {
          margin-top: 1rem;
          font-size: 0.9rem;
          min-height: 44px;
        }

        // Small mobile
        @media (max-width: 360px) {
          margin-top: 0.75rem;
          font-size: 0.85rem;
          min-height: 40px;
        }

        // Button states with gradient theme
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #2563eb 100%) !important;
          box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4) !important;
          transform: translateY(-1px);
        }

        &:active:not(:disabled) {
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1e3a8a 100%) !important;
          box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;
          transform: translateY(0);
        }

        &:disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 50%, #9ca3af 100%) !important;
          color: rgba(255, 255, 255, 0.7) !important;
          box-shadow: none !important;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Loading overlay styling
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

// Simplified responsive Material Design form field customization
::ng-deep .mat-mdc-form-field {
  margin-bottom: 1rem;

  // Tablets
  @media (max-width: 768px) {
    margin-bottom: 0.875rem;
  }

  // Mobile
  @media (max-width: 480px) {
    margin-bottom: 0.75rem;
  }

  // Small mobile
  @media (max-width: 360px) {
    margin-bottom: 0.625rem;
  }

  .mat-mdc-text-field-wrapper {
    .mat-mdc-form-field-flex {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-notch,
        .mat-mdc-form-field-outline-end {
          border-color: rgba(0, 0, 0, 0.12);
          border-width: 1px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
    }
  }

  // Enhanced theming to complement the gradient design
  &.mat-focused {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-flex {
        .mat-mdc-form-field-outline {
          .mat-mdc-form-field-outline-start,
          .mat-mdc-form-field-outline-notch,
          .mat-mdc-form-field-outline-end {
            border-color: #1e40af !important;
            border-width: 2px !important;
            box-shadow: 0 0 0 1px rgba(30, 64, 175, 0.1);
          }
        }
      }
    }
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: #1e40af !important;
  }

  // Prefix and suffix icon theming
  .mat-mdc-form-field-icon-prefix {
    color: #64748b;
    transition: color 0.2s ease;
  }

  &.mat-focused .mat-mdc-form-field-icon-prefix {
    color: #1e40af;
  }

  .mat-mdc-form-field-icon-suffix {
    .mat-mdc-icon-button {
      color: #64748b;
      transition: all 0.2s ease;

      &:hover {
        color: #1e40af;
        background-color: rgba(30, 64, 175, 0.05);
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Unique animations for set-password page (different from login)

// Secure floating animation with gentle sway and glow
@keyframes secureFloat {
  0% {
    transform: translateY(0px) translateX(0px) scale(1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
  25% {
    transform: translateY(-15px) translateX(8px) scale(1.05);
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.2);
  }
  50% {
    transform: translateY(-25px) translateX(0px) scale(1.1);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.15);
  }
  75% {
    transform: translateY(-15px) translateX(-8px) scale(1.05);
    box-shadow: 0 0 25px rgba(34, 197, 94, 0.2);
  }
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
}

// Secure rotation with scaling effect
@keyframes secureRotateScale {
  0% {
    transform: rotate(0deg) scale(1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.08);
  }
  25% {
    transform: rotate(90deg) scale(1.2);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }
  50% {
    transform: rotate(180deg) scale(0.8);
    box-shadow: 0 0 25px rgba(255, 255, 255, 0.12);
  }
  75% {
    transform: rotate(270deg) scale(1.2);
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.15);
  }
  100% {
    transform: rotate(360deg) scale(1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.08);
  }
}

// Secure breathing effect with color transitions
@keyframes secureBreathe {
  0% {
    transform: scale(1);
    opacity: 0.25;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
  50% {
    transform: scale(1.4);
    opacity: 0.4;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  100% {
    transform: scale(1);
    opacity: 0.25;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
}

// Secure sliding with directional changes
@keyframes secureSlide {
  0% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
  25% {
    transform: translateX(15px) translateY(-10px) rotate(15deg);
    text-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
  }
  50% {
    transform: translateX(0px) translateY(-20px) rotate(0deg);
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  75% {
    transform: translateX(-15px) translateY(-10px) rotate(-15deg);
    text-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
  }
  100% {
    transform: translateX(0px) translateY(0px) rotate(0deg);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  }
}

// Secure wave effect with gradient flow
@keyframes secureWave {
  0% {
    transform: scaleY(1) scaleX(1);
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(59, 130, 246, 0.2), rgba(255, 255, 255, 0.1));
  }
  33% {
    transform: scaleY(1.3) scaleX(0.8);
    background: linear-gradient(90deg, rgba(34, 197, 94, 0.2), rgba(255, 255, 255, 0.15), rgba(59, 130, 246, 0.2));
  }
  66% {
    transform: scaleY(0.7) scaleX(1.2);
    background: linear-gradient(90deg, rgba(168, 85, 247, 0.2), rgba(34, 197, 94, 0.15), rgba(255, 255, 255, 0.1));
  }
  100% {
    transform: scaleY(1) scaleX(1);
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(59, 130, 246, 0.2), rgba(255, 255, 255, 0.1));
  }
}

// Secure flip animation with 3D effect
@keyframes secureFlip {
  0% {
    transform: rotateY(0deg) rotateX(0deg);
    text-shadow: 0 0 12px rgba(255, 255, 255, 0.15);
  }
  25% {
    transform: rotateY(90deg) rotateX(15deg);
    text-shadow: 0 0 18px rgba(34, 197, 94, 0.25);
  }
  50% {
    transform: rotateY(180deg) rotateX(0deg);
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  75% {
    transform: rotateY(270deg) rotateX(-15deg);
    text-shadow: 0 0 18px rgba(168, 85, 247, 0.25);
  }
  100% {
    transform: rotateY(360deg) rotateX(0deg);
    text-shadow: 0 0 12px rgba(255, 255, 255, 0.15);
  }
}

// Secure cross rotation with pulsing
@keyframes secureCrossRotate {
  0% {
    transform: rotate(0deg) scale(1);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.18);
  }
  20% {
    transform: rotate(72deg) scale(1.1);
    text-shadow: 0 0 20px rgba(34, 197, 94, 0.25);
  }
  40% {
    transform: rotate(144deg) scale(0.9);
    text-shadow: 0 0 25px rgba(59, 130, 246, 0.3);
  }
  60% {
    transform: rotate(216deg) scale(1.2);
    text-shadow: 0 0 20px rgba(168, 85, 247, 0.25);
  }
  80% {
    transform: rotate(288deg) scale(0.8);
    text-shadow: 0 0 18px rgba(34, 197, 94, 0.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.18);
  }
}

// Scroll hint animation for mobile devices
@keyframes scrollHint {
  0%, 100% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 0.6;
    transform: translateY(10px);
  }
}

// Touch-friendly improvements for mobile
@media (max-width: 768px) {
  // Ensure minimum touch target sizes
  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      min-height: 48px;
    }
  }

  ::ng-deep .mat-mdc-icon-button {
    min-width: 44px;
    min-height: 44px;
  }
}

// Ensure content is always visible with proper spacing
@media (max-width: 768px) {
  .set-password-container {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }
}

// Hide browser's default password visibility toggle
.no-browser-password-toggle {
  // Hide Edge/IE password reveal button
  &::-ms-reveal,
  &::-ms-clear {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  // Hide Chrome/Safari password reveal button
  &::-webkit-credentials-auto-fill-button,
  &::-webkit-strong-password-auto-fill-button {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
    position: absolute !important;
    right: 0 !important;
  }

  // Additional browser-specific hiding
  &[type="password"]::-webkit-textfield-decoration-container {
    visibility: hidden !important;
  }

  // Firefox password reveal button (if any)
  &::-moz-reveal {
    display: none !important;
  }
}