import { Routes } from '@angular/router';
import { setPasswordGuard } from './auth/guards/set-password.guard';

export const routes: Routes = [
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then(m => m.HomeComponent),
    title: 'Home'
  },
  {
    path: 'set-password',
    loadComponent: () => import('./set-password/set-password.component').then(m => m.SetPasswordComponent),
    canActivate: [setPasswordGuard],
    title: 'Set Password'
  },
  {
    path: '',
    redirectTo: '/home',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: '/home'
  }
];
