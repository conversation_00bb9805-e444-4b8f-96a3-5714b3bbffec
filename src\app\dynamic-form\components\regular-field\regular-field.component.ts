import { Component, Input, Output, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, OnChang<PERSON>, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';
import { LanguageFieldComponent } from '../language-field/language-field.component';

@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent,
    LanguageFieldComponent
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements On<PERSON><PERSON>t, OnD<PERSON>roy, OnChanges {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)
  @Input() groupIndex?: number; // Optional group index for grouped fields
  @Input() nestedGroupIndex?: number; // Optional nested group index for nested groups

  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  // Note: Dropdown properties moved to unified DropdownComponent
  // API caching is now handled by the unified DropdownComponent

  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Dropdown preloading is now handled by the unified DropdownComponent
    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
    
    // Ensure form control exists for this field
    this.ensureFormControlExists();
  }

  /**
   * Ensure form control exists for this field
   */
  private ensureFormControlExists(): void {
    if (!this.form || !this.field) return;
    
    const formControl = this.form.get(this.field.fieldName);
    if (!formControl) {
      // Try to create the form control if it doesn't exist
      const validators = this.field.mandatory ? Validators.required : null;
      let control;
      switch (this.field.type) {
        case "boolean":
          control = new FormControl(false, validators);
          break;
        case "date":
          control = new FormControl(null, validators);
          break;
        default:
          control = new FormControl("", validators);
          break;
      }
      this.form.addControl(this.field.fieldName, control);
      
      // Disable control if noInput is true
      if (this.field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.field.fieldName);
    if (formControl) {
      if (this.isViewMode || this.field.noInput) {
        if (formControl.enabled) {
          formControl.disable({ emitEvent: false });
        }
      } else {
        if (formControl.disabled) {
          formControl.enable({ emitEvent: false });
        }
      }
    }
  }

  ngOnChanges(): void {
    // Update form control disabled state when inputs change
    this.updateFormControlDisabledState();
  }

  ngOnDestroy() {
    // Cleanup handled by unified DropdownComponent
  }

  // Note: All dropdown methods moved to unified DropdownComponent
  // Utility methods are now handled by the unified DropdownComponent

  // Note: Group path parsing and array access handled by parent component

  // Dropdown preloading is now handled by the unified DropdownComponent

  // Configuration methods for unified dropdown component
  getTypeDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'type',
      queryBuilderId: 'fieldType',
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No types found',
      tooltip: 'Show type suggestions',
      enableLazyLoading: false, // Disabled by default for performance
      preloadThreshold: 20,
      cacheTimeout: 600000, // 10 minutes for types
      loadOnFocus: true,
      loadOnOpen: true,
      loadOnSearch: true,
      maxCacheSize: 50,
      enableCacheCleanup: true
    };
  }

  getForeignKeyDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'foreignKey',
      queryBuilderId: 'formDefinition',
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No foreign keys found',
      tooltip: 'Show foreign key suggestions',
      enableLazyLoading: false, // Disabled by default for performance
      preloadThreshold: 30,
      cacheTimeout: 900000, // 15 minutes for foreign keys
      loadOnFocus: true,
      loadOnOpen: true,
      loadOnSearch: true,
      maxCacheSize: 30,
      enableCacheCleanup: true
    };
  }

  getRegularDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'regular',
      queryBuilderId: field.foreginKey,
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show options',
      enableLazyLoading: false, // Disabled by default for performance
      preloadThreshold: 100, // Higher threshold for regular dropdowns
      cacheTimeout: 300000, // 5 minutes for regular data
      loadOnFocus: true,
      loadOnOpen: true,
      loadOnSearch: true,
      maxCacheSize: 200, // Larger cache for regular dropdowns
      enableCacheCleanup: true
    };
  }

  getLookupDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'lookup',
      searchEnabled: true,
      placeholder: `Select ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show lookup options',
      enableLazyLoading: false, // Lookup dropdowns use preloaded options
      preloadThreshold: 0,
      cacheTimeout: 1800000, // 30 minutes for lookup data
      loadOnFocus: false,
      loadOnOpen: false,
      loadOnSearch: false,
      maxCacheSize: 50,
      enableCacheCleanup: true
    };
  }

  // Event handler for unified dropdown component
  onDropdownValueChange(event: DropdownValueChangeEvent): void {
    // Map the unique ID back to the original field name
    const originalFieldName = this.extractOriginalFieldName(event.fieldName);
    
    // Emit the field value change event for parent component
    this.fieldValueChange.emit({
      fieldName: originalFieldName,
      value: event.value
    });
  }

  // Extract original field name from unique ID
  private extractOriginalFieldName(uniqueId: string): string {
    // Handle complex unique IDs with group, nested, and multi indices
    if (uniqueId.includes('_group_') || uniqueId.includes('_nested_') || uniqueId.includes('_multi_')) {
      // Split by underscores and take the first part (original field name)
      const parts = uniqueId.split('_');
      return parts[0];
    }
    return uniqueId;
  }

  // Helper method to get FormControl with proper typing
  getFormControl(fieldName: string): any {
    return this.form.get(fieldName);
  }

  // Generate unique ID for dropdown fields to prevent conflicts in multi-field scenarios
  getUniqueFieldId(fieldName: string): string {
    let uniqueId = fieldName;
    
    // Add group index if available
    if (this.groupIndex !== undefined) {
      uniqueId += `_group_${this.groupIndex}`;
    }
    
    // Add nested group index if available
    if (this.nestedGroupIndex !== undefined) {
      uniqueId += `_nested_${this.nestedGroupIndex}`;
    }
    
    // Add multi index if available
    if (this.multiIndex) {
      uniqueId += `_multi_${this.multiIndex}`;
    }
    
    return uniqueId;
  }
}
