<div class="filter-group-container">
  <!-- Group Header -->
  <div class="group-header">
    <mat-form-field class="logic-selector" appearance="fill">
      <mat-label>Group Logic</mat-label>
      <mat-select [(ngModel)]="group.logic" (ngModelChange)="emitChange()">
        <mat-option value="AND">AND</mat-option>
        <mat-option value="OR">OR</mat-option>
      </mat-select>
    </mat-form-field>

    <div class="group-actions">
      <button mat-button (click)="addCondition()" title="Add Condition">
        <mat-icon>add</mat-icon> Condition
      </button>
      <button mat-button (click)="addSubGroup()" title="Add Sub Group">
        <mat-icon>add_circle_outline</mat-icon> Group
      </button>
      @if (canRemove) {
        <button mat-icon-button (click)="onRemoveGroup()" title="Remove This Group">
          <mat-icon>delete</mat-icon>
        </button>
      }
    </div>
  </div>

  <!-- Conditions -->
  <div class="conditions-section">
    @for (condition of group.conditions; track i; let i = $index) {
      <div class="filter-row">
        <mat-form-field appearance="fill" class="field-select">
          <mat-label>Field</mat-label>
          <mat-select [(ngModel)]="condition.field" (ngModelChange)="onFieldChange(condition)">
            <mat-option value="">Select Field</mat-option>
            @for (field of fields; track field) {
              <mat-option [value]="field.selectionField">
                {{ field.selectionField }}
              </mat-option>
            }
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="fill" class="operator-select">
          <mat-label>Operator</mat-label>
          <mat-select [(ngModel)]="condition.operator" (ngModelChange)="onConditionChange()">
            @if (!condition.availableOperators || condition.availableOperators.length === 0) {
              <mat-option value="" disabled>
                Loading/No operators...
              </mat-option>
            }
            @for (op of condition.availableOperators; track op) {
              <mat-option [value]="op.ID">
                {{ op.description || op.ID }}
              </mat-option>
            }
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="fill" class="value-input">
          <mat-label>Value</mat-label>
          <input matInput [(ngModel)]="condition.value" [type]="getInputType(condition.field)" (ngModelChange)="onConditionChange()" placeholder="Enter value">
        </mat-form-field>
        
        <button mat-icon-button (click)="removeCondition(i)" title="Remove Condition" class="remove-condition-btn">
          <mat-icon>remove_circle_outline</mat-icon>
        </button>
      </div>
    }
  </div>

  <!-- Nested Groups (Children) -->
  @if (group.children && group.children.length > 0) {
    <div class="nested-groups">
      @for (child of group.children; track child) {
        <div class="nested-group">
          <app-filter-group
            [group]="child"
            [fields]="fields"
            [canRemove]="true"
            (remove)="removeChild(child)"
            (change)="emitChange()">
          </app-filter-group>
        </div>
      }
    </div>
  }
</div>