import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { LazyLoadingService, LazyLoadingConfig } from './lazy-loading.service';

describe('LazyLoadingService', () => {
  let service: LazyLoadingService;
  let httpMock: HttpTestingController;

  const mockData = [
    { id: 1, name: 'Item 1' },
    { id: 2, name: 'Item 2' },
    { id: 3, name: 'Item 3' }
  ];

  const testConfig: LazyLoadingConfig = {
    cacheTimeout: 300000,
    maxCacheSize: 50,
    enableCacheCleanup: true,
    enablePerformanceMonitoring: true
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [LazyLoadingService]
    });
    service = TestBed.inject(LazyLoadingService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
    service.clearAllCache();
  });

  describe('Data Loading', () => {
    it('should load data from API', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(data => {
        expect(data).toEqual(mockData);
        done();
      });

      const req = httpMock.expectOne(endpoint);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);
      req.flush(mockData);
    });

    it('should return cached data on subsequent requests', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      // First request
      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(data => {
        expect(data).toEqual(mockData);

        // Second request should return cached data
        service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(cachedData => {
          expect(cachedData).toEqual(mockData);
          done();
        });
      });

      // Only one HTTP request should be made
      const req = httpMock.expectOne(endpoint);
      req.flush(mockData);
    });

    it('should handle API errors gracefully', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(data => {
        expect(data).toEqual([]);
        done();
      });

      const req = httpMock.expectOne(endpoint);
      req.error(new ErrorEvent('Network error'));
    });
  });

  describe('Cache Management', () => {
    it('should cache data correctly', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(() => {
        const stats = service.getCacheStats();
        expect(stats.size).toBe(1);
        expect(stats.keys).toContain(cacheKey);
        done();
      });

      const req = httpMock.expectOne(endpoint);
      req.flush(mockData);
    });

    it('should clear specific cache entry', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(() => {
        service.clearCache(cacheKey);
        const stats = service.getCacheStats();
        expect(stats.size).toBe(0);
        done();
      });

      const req = httpMock.expectOne(endpoint);
      req.flush(mockData);
    });

    it('should clear all cache', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };

      // Load multiple cache entries
      service.loadData(endpoint, payload, 'key1', testConfig).subscribe(() => {
        service.loadData(endpoint, payload, 'key2', testConfig).subscribe(() => {
          service.clearAllCache();
          const stats = service.getCacheStats();
          expect(stats.size).toBe(0);
          done();
        });
      });

      const req1 = httpMock.expectOne(endpoint);
      req1.flush(mockData);
      const req2 = httpMock.expectOne(endpoint);
      req2.flush(mockData);
    });

    it('should respect cache size limits', (done) => {
      const config = { ...testConfig, maxCacheSize: 2 };
      const endpoint = '/api/test';
      const payload = { query: 'test' };

      // Load more entries than cache size limit
      service.loadData(endpoint, payload, 'key1', config).subscribe(() => {
        service.loadData(endpoint, payload, 'key2', config).subscribe(() => {
          service.loadData(endpoint, payload, 'key3', config).subscribe(() => {
            const stats = service.getCacheStats();
            expect(stats.size).toBeLessThanOrEqual(2);
            done();
          });
        });
      });

      const req1 = httpMock.expectOne(endpoint);
      req1.flush(mockData);
      const req2 = httpMock.expectOne(endpoint);
      req2.flush(mockData);
      const req3 = httpMock.expectOne(endpoint);
      req3.flush(mockData);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance metrics when enabled', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(() => {
        const metrics = service.getPerformanceMetrics(cacheKey);
        expect(metrics).toBeTruthy();
        expect(metrics!.totalRequests).toBe(1);
        expect(metrics!.cacheMisses).toBe(1);
        done();
      });

      const req = httpMock.expectOne(endpoint);
      req.flush(mockData);
    });

    it('should track cache hits correctly', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      // First request (cache miss)
      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(() => {
        // Second request (cache hit)
        service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(() => {
          const metrics = service.getPerformanceMetrics(cacheKey);
          expect(metrics!.totalRequests).toBe(2);
          expect(metrics!.cacheHits).toBe(1);
          expect(metrics!.cacheMisses).toBe(1);
          expect(metrics!.cacheHitRate).toBe(50);
          done();
        });
      });

      const req = httpMock.expectOne(endpoint);
      req.flush(mockData);
    });

    it('should reset performance metrics', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };
      const cacheKey = 'test-key';

      service.loadData(endpoint, payload, cacheKey, testConfig).subscribe(() => {
        service.resetPerformanceMetrics(cacheKey);
        const metrics = service.getPerformanceMetrics(cacheKey);
        expect(metrics).toBeNull();
        done();
      });

      const req = httpMock.expectOne(endpoint);
      req.flush(mockData);
    });

    it('should get all performance metrics', (done) => {
      const endpoint = '/api/test';
      const payload = { query: 'test' };

      service.loadData(endpoint, payload, 'key1', testConfig).subscribe(() => {
        service.loadData(endpoint, payload, 'key2', testConfig).subscribe(() => {
          const allMetrics = service.getAllPerformanceMetrics();
          expect(Object.keys(allMetrics).length).toBe(2);
          expect(allMetrics['key1']).toBeTruthy();
          expect(allMetrics['key2']).toBeTruthy();
          done();
        });
      });

      const req1 = httpMock.expectOne(endpoint);
      req1.flush(mockData);
      const req2 = httpMock.expectOne(endpoint);
      req2.flush(mockData);
    });
  });
});
