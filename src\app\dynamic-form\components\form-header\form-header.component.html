<div class="form-header">
  <div class="horizontal-container">
    <div class="form-field">
      <!-- <label>ID</label> -->
      <p>{{ form.get('ID')?.value }}</p>
    </div>

    <div class="button-group">
    <!-- Button Container with Rounded Corners -->
    <div class="button-container">
      <!-- Delete button -->
      @if (!isViewMode) {
        <button mat-raised-button color="warn" type="button" matTooltip="Delete"
                class="form-action-button delete-button" (click)="onDeleteRecord()"
                [ngClass]="{ 'readonly-button': isAuth }">
          <mat-icon>delete</mat-icon>
        </button>
      }

      <!-- Reject button -->
      @if (!isViewMode) {
        <button mat-raised-button color="warn" type="button" matTooltip="Reject"
                class="form-action-button reject-button" (click)="onRejectRecord()"
                [ngClass]="{ 'readonly-button': isAuth }">
          <mat-icon>cancel</mat-icon>
        </button>
      }

      <!-- back button -->
      <button mat-raised-button color="primary" type="button" (click)="onGoBack()" matTooltip="Back"
              class="form-action-button back-button">
        <mat-icon>arrow_back</mat-icon>
      </button>

      <!-- Authorize button -->
      @if (!isViewMode) {
        <button mat-raised-button color="accent" type="button" (click)="onAuthorizeRecord()" matTooltip="Authorize"
                class="form-action-button authorize-button">
          <mat-icon>verified</mat-icon>
        </button>
      }

      <!-- Validate button -->
      @if (!isViewMode) {
        <button mat-raised-button color="accent" type="button" (click)="onValidateRecord()" matTooltip="Validate"
                class="form-action-button validate-button">
          <mat-icon>check_circle</mat-icon>
        </button>
      }

      <!-- submit button -->
      @if (!isViewMode) {
        <button mat-raised-button color="primary" type="submit" [disabled]="isViewMode" matTooltip="Submit"
                class="form-action-button submit-button" (click)="onSubmitForm()">
          <mat-icon>send</mat-icon>
        </button>
      }

      <!-- View toggle button -->
      <button mat-raised-button color="primary" type="button" (click)="onToggleViewMode()"
              matTooltip="{{ isRowView ? 'Switch to Nested View' : 'Switch to Row View' }}"
              class="form-action-button toggle-view-button">
        <mat-icon>{{ isRowView ? 'view_list' : 'view_module' }}</mat-icon>
      </button>

      <!-- Copy button -->
      @if (!isViewMode) {
        <button mat-raised-button color="accent" type="button" (click)="onCopyFormData()" matTooltip="Copy Form Data"
                class="form-action-button copy-button">
          <mat-icon>content_copy</mat-icon>
        </button>
      }

      <!-- Paste button -->
      @if (!isViewMode) {
        <button mat-raised-button color="accent" type="button" (click)="onPasteFormData()" matTooltip="Paste Form Data"
                class="form-action-button paste-button">
          <mat-icon>content_paste</mat-icon>
        </button>
      }
    </div>
    
    @if (errorMessage) {
      <div class="error-message">{{ errorMessage }}</div>
    }
  </div>
  </div>
</div>
