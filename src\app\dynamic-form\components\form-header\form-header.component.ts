import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-form-header',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './form-header.component.html',
  styleUrl: './form-header.component.scss'
})
export class FormHeaderComponent {
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() isAuth: boolean = true;
  @Input() isRowView: boolean = false;
  @Input() errorMessage: string = '';
  
  @Output() toggleViewMode = new EventEmitter<void>();
  @Output() submitForm = new EventEmitter<void>();
  @Output() validateRecord = new EventEmitter<void>();
  @Output() authorizeRecord = new EventEmitter<void>();
  @Output() goBack = new EventEmitter<void>();
  @Output() rejectRecord = new EventEmitter<void>();
  @Output() deleteRecord = new EventEmitter<void>();
  @Output() setFormLoading = new EventEmitter<boolean>();
  @Output() copyFormData = new EventEmitter<void>();
  @Output() pasteFormData = new EventEmitter<void>();

  onToggleViewMode(): void {
    this.toggleViewMode.emit();
  }

  onSubmitForm(): void {
    this.setFormLoading.emit(true);
    this.submitForm.emit();
  }

  onValidateRecord(): void {
    this.setFormLoading.emit(true);
    this.validateRecord.emit();
  }

  onAuthorizeRecord(): void {
    this.setFormLoading.emit(true);
    this.authorizeRecord.emit();
  }

  onGoBack(): void {
    this.goBack.emit();
  }

  onRejectRecord(): void {
    this.setFormLoading.emit(true);
    this.rejectRecord.emit();
  }

  onDeleteRecord(): void {
    this.setFormLoading.emit(true);
    this.deleteRecord.emit();
  }

  onCopyFormData(): void {
    this.copyFormData.emit();
  }

  onPasteFormData(): void {
    this.pasteFormData.emit();
  }
}
