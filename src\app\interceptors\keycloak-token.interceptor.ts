import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { KeycloakService } from '../services/keycloak.service';
import { from } from 'rxjs';
import { switchMap } from 'rxjs/operators';

export const keycloakTokenInterceptor: HttpInterceptorFn = (req, next) => {
  const keycloakService = inject(KeycloakService);
  const keycloakAuth = keycloakService['keycloakAuth'];
  if (keycloakAuth && keycloakAuth.updateToken) {
    return from(keycloakAuth.updateToken(30)).pipe(
      switchMap((refreshed) => {
        if (refreshed) {
        }
        const token = keycloakService.getToken();
        if (token) {
          const cloned = req.clone({
            setHeaders: {
              Authorization: `Bearer ${token}`
            }
          });
          return next(cloned);
        }
        return next(req);
      })
    );
  } else {
    const token = keycloakService.getToken();
    if (token) {
      const cloned = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
      return next(cloned);
    }
    return next(req);
  }
}; 