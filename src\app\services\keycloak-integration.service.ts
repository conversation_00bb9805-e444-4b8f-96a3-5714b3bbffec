import { Injectable } from '@angular/core';
import { KeycloakService } from './keycloak.service';
import { SessionStorageService } from './session-storage.service';
import { AuthenticationService } from './authentication.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { firstValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class KeycloakIntegrationService {
  
  constructor(
    private keycloakService: KeycloakService,
    private sessionStorage: SessionStorageService,
    private authService: AuthenticationService,
    private http: HttpClient
  ) {}

  /**
   * Initialize Keycloak and fetch user data
   */
  async initializeKeycloak(): Promise<boolean> {
    try {
      // Initialize Keycloak
      await this.keycloakService.init();
      
      // Get username from Keycloak token
      let username: string | undefined;
      if (this.keycloakService['keycloakAuth']?.tokenParsed?.['preferred_username']) {
        username = this.keycloakService['keycloakAuth'].tokenParsed['preferred_username'];
      } else {
        const kcProfile = await this.keycloakService.loadUserProfile();
        username = kcProfile?.username;
      }
      
      const token = this.keycloakService.getToken?.() || this.keycloakService['keycloakAuth']?.token;
      
      if (username && token) {
        await this.fetchUserData(username, token);
        return true;
      } else {
        this.setDefaultLanguages();
        return false;
      }
    } catch (error) {
      this.setDefaultLanguages();
      return false;
    }
  }

  /**
   * Fetch user data from backend
   */
  private async fetchUserData(username: string, token: string): Promise<void> {
    try {
      // Fetch user profile from backend
      const profile = await firstValueFrom(this.authService.getProfile(username, token));
      this.sessionStorage.setUserProfile(profile);
      
      // Fetch user detail and store privileges/selectedBranch
      const userDetail: any = await firstValueFrom(
        this.http.get(`${environment.baseURL}/api/user/detail`, {
          headers: { Authorization: `Bearer ${token}` }
        })
      );
      
      if (userDetail && userDetail.privileges) {
        this.sessionStorage.setUserPrivileges(userDetail.privileges);
        if (userDetail.privileges.length > 0) {
          this.sessionStorage.setSelectedBranch(userDetail.privileges[0]);
        }
      }
      
      // Handle user languages
      await this.handleUserLanguages(userDetail);
      
    } catch (e) {
      this.setDefaultLanguages();
    }
  }

  /**
   * Handle user languages from Keycloak profile or backend
   */
  private async handleUserLanguages(userDetail?: any): Promise<void> {
    let userLanguages = ['en']; // Default to English
    
    try {
      // Try to get languages from Keycloak profile
      const kcProfile = await this.keycloakService.loadUserProfile();
      if (kcProfile?.attributes?.['locale']) {
        const locale = Array.isArray(kcProfile.attributes['locale']) 
          ? kcProfile.attributes['locale'][0] 
          : kcProfile.attributes['locale'];
        userLanguages = [locale, 'en']; // Add English as fallback
      }
      
      // If backend provides languages, use those instead
      if (userDetail && userDetail.languages && Array.isArray(userDetail.languages)) {
        userLanguages = userDetail.languages;
      }
      
      // Store languages in sessionStorage
      this.sessionStorage.setUserLanguages(userLanguages);
      
    } catch (error) {
      this.setDefaultLanguages();
    }
  }

  /**
   * Set default languages
   */
  private setDefaultLanguages(): void {
    this.sessionStorage.setUserLanguages(['en']);
  }

  /**
   * Check if Keycloak is properly initialized
   */
  isKeycloakReady(): boolean {
    return this.keycloakService.isLoggedIn() && this.sessionStorage.isLoggedIn();
  }

  /**
   * Get current user info for debugging
   */
  getCurrentUserInfo(): any {
    return {
      keycloakLoggedIn: this.keycloakService.isLoggedIn(),
      sessionLoggedIn: this.sessionStorage.isLoggedIn(),
      profile: this.sessionStorage.getUserProfile(),
      privileges: this.sessionStorage.getUserPrivileges(),
      languages: this.sessionStorage.getUserLanguages(),
      selectedBranch: this.sessionStorage.getSelectedBranch()
    };
  }
} 