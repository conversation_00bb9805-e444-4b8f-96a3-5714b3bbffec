import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { InitialInputComponent } from './initial-input.component';

describe('InitialInputComponent', () => {
  let component: InitialInputComponent;
  let fixture: ComponentFixture<InitialInputComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        InitialInputComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        NoopAnimationsModule
      ],
      providers: [FormBuilder]
    })
    .compileComponents();

    fixture = TestBed.createComponent(InitialInputComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);
    
    // Setup required inputs
    component.form = formBuilder.group({
      ID: ['']
    });
    component.tableName = 'testTable';
    component.screenName = 'testScreen';
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit loadDataAndBuildForm when add button is clicked', () => {
    spyOn(component.loadDataAndBuildForm, 'emit');
    component.onAddClick();
    expect(component.loadDataAndBuildForm.emit).toHaveBeenCalled();
  });

  it('should emit loadDataAndBuildForm when edit button is clicked', () => {
    spyOn(component.loadDataAndBuildForm, 'emit');
    component.onEditClick();
    expect(component.loadDataAndBuildForm.emit).toHaveBeenCalled();
  });

  it('should emit viewData when view button is clicked', () => {
    spyOn(component.viewData, 'emit');
    component.onViewClick();
    expect(component.viewData.emit).toHaveBeenCalled();
  });

  it('should return correct input class based on validation state', () => {
    component.showValidation = false;
    expect(component.getInputClass()).toBe('');
    
    component.showValidation = true;
    expect(component.getInputClass()).toBe('invalid-input');
  });

  it('should toggle dropdown visibility', () => {
    expect(component.showIdDropdown).toBeFalse();
    component.toggleIdDropdown();
    // Note: This would normally trigger loadAllIds() which requires HTTP
    // In a real test, we'd mock the HTTP service
  });

  it('should clear timeout on destroy', () => {
    component.idSearchTimeout = setTimeout(() => {}, 1000);
    spyOn(window, 'clearTimeout');
    component.ngOnDestroy();
    expect(clearTimeout).toHaveBeenCalled();
  });
});
