import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { KeycloakService } from './keycloak.service';
import { environment } from '../../environments/environment';

export interface LazyLoadingConfig {
  cacheTimeout?: number;              // Cache expiration time in ms (default: 300000 = 5 min)
  maxCacheSize?: number;              // Maximum number of cached entries (default: 50)
  enableCacheCleanup?: boolean;       // Enable automatic cache cleanup (default: true)
  enablePerformanceMonitoring?: boolean; // Enable performance monitoring (default: false)
}

export interface PerformanceMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageLoadTime: number;
  totalLoadTime: number;
  cacheHitRate: number;
}

@Injectable({
  providedIn: 'root'
})
export class LazyLoadingService {
  private http = inject(HttpClient);
  private keycloakService = inject(KeycloakService);
  
  // Cache management
  private cache: { [key: string]: any[] } = {};
  private cacheTimestamps: { [key: string]: number } = {};
  private cacheAccessTimes: { [key: string]: number } = {};

  // Performance monitoring
  private performanceMetrics: { [key: string]: PerformanceMetrics } = {};
  private loadStartTimes: { [key: string]: number } = {};

  // Default configuration
  private defaultConfig: LazyLoadingConfig = {
    cacheTimeout: 300000, // 5 minutes
    maxCacheSize: 50,
    enableCacheCleanup: true,
    enablePerformanceMonitoring: false
  };

  constructor() {
    // Setup periodic cache cleanup
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 300000); // Every 5 minutes
  }

  /**
   * Load data with lazy loading and caching
   */
  loadData(
    endpoint: string,
    payload: any,
    cacheKey: string,
    config: LazyLoadingConfig = {}
  ): Observable<any[]> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();

    // Initialize performance metrics if monitoring is enabled
    if (finalConfig.enablePerformanceMonitoring) {
      this.initializeMetrics(cacheKey);
      this.loadStartTimes[cacheKey] = startTime;
    }

    // Check if we have valid cached data
    if (this.cache[cacheKey] && !this.isCacheExpired(cacheKey, finalConfig.cacheTimeout!)) {
      this.updateCacheAccess(cacheKey);

      // Record cache hit
      if (finalConfig.enablePerformanceMonitoring) {
        this.recordCacheHit(cacheKey, startTime);
      }

      return of(this.cache[cacheKey]);
    }

    // Record cache miss
    if (finalConfig.enablePerformanceMonitoring) {
      this.recordCacheMiss(cacheKey);
    }

    // Load from API
    return this.http.post<any[]>(endpoint, payload, this.getAuthHeaders()).pipe(
      tap((response: any[]) => {
        if (Array.isArray(response)) {
          this.cache[cacheKey] = response;
          this.setCacheTimestamp(cacheKey);
          this.cleanupCache(finalConfig.maxCacheSize!);

          // Record load completion
          if (finalConfig.enablePerformanceMonitoring) {
            this.recordLoadCompletion(cacheKey, startTime);
          }
        }
      }),
      catchError(() => {
        // Record load completion even on error
        if (finalConfig.enablePerformanceMonitoring) {
          this.recordLoadCompletion(cacheKey, startTime);
        }
        // Return empty array on error
        return of([]);
      })
    );
  }

  /**
   * Clear specific cache entry
   */
  clearCache(cacheKey: string): void {
    delete this.cache[cacheKey];
    delete this.cacheTimestamps[cacheKey];
    delete this.cacheAccessTimes[cacheKey];
  }

  /**
   * Clear all cache
   */
  clearAllCache(): void {
    this.cache = {};
    this.cacheTimestamps = {};
    this.cacheAccessTimes = {};
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: Object.keys(this.cache).length,
      keys: Object.keys(this.cache)
    };
  }

  /**
   * Get performance metrics for a specific cache key
   */
  getPerformanceMetrics(cacheKey: string): PerformanceMetrics | null {
    return this.performanceMetrics[cacheKey] || null;
  }

  /**
   * Get all performance metrics
   */
  getAllPerformanceMetrics(): { [key: string]: PerformanceMetrics } {
    return { ...this.performanceMetrics };
  }

  /**
   * Reset performance metrics
   */
  resetPerformanceMetrics(cacheKey?: string): void {
    if (cacheKey) {
      delete this.performanceMetrics[cacheKey];
    } else {
      this.performanceMetrics = {};
    }
  }

  // Private helper methods
  private getAuthHeaders() {
    const token = this.keycloakService.getToken();
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`
      })
    };
  }

  private isCacheExpired(cacheKey: string, timeout: number): boolean {
    const timestamp = this.cacheTimestamps[cacheKey];
    if (!timestamp) return true;
    return Date.now() - timestamp > timeout;
  }

  private setCacheTimestamp(cacheKey: string): void {
    this.cacheTimestamps[cacheKey] = Date.now();
    this.cacheAccessTimes[cacheKey] = Date.now();
  }

  private updateCacheAccess(cacheKey: string): void {
    this.cacheAccessTimes[cacheKey] = Date.now();
  }

  private cleanupCache(maxSize: number): void {
    const cacheKeys = Object.keys(this.cache);
    
    if (cacheKeys.length <= maxSize) return;
    
    // Sort by last access time (least recently used first)
    const sortedKeys = cacheKeys.sort((a, b) => {
      const accessTimeA = this.cacheAccessTimes[a] || 0;
      const accessTimeB = this.cacheAccessTimes[b] || 0;
      return accessTimeA - accessTimeB;
    });
    
    // Remove oldest entries
    const keysToRemove = sortedKeys.slice(0, cacheKeys.length - maxSize);
    keysToRemove.forEach(key => {
      delete this.cache[key];
      delete this.cacheTimestamps[key];
      delete this.cacheAccessTimes[key];
    });
  }

  private cleanupExpiredCache(): void {
    const now = Date.now();
    const timeout = this.defaultConfig.cacheTimeout!;

    Object.keys(this.cacheTimestamps).forEach(key => {
      if (now - this.cacheTimestamps[key] > timeout) {
        delete this.cache[key];
        delete this.cacheTimestamps[key];
        delete this.cacheAccessTimes[key];
      }
    });
  }

  // Performance monitoring helper methods
  private initializeMetrics(cacheKey: string): void {
    if (!this.performanceMetrics[cacheKey]) {
      this.performanceMetrics[cacheKey] = {
        totalRequests: 0,
        cacheHits: 0,
        cacheMisses: 0,
        averageLoadTime: 0,
        totalLoadTime: 0,
        cacheHitRate: 0
      };
    }
  }

  private recordCacheHit(cacheKey: string, startTime: number): void {
    const metrics = this.performanceMetrics[cacheKey];
    if (metrics) {
      metrics.totalRequests++;
      metrics.cacheHits++;
      metrics.cacheHitRate = (metrics.cacheHits / metrics.totalRequests) * 100;

      // Cache hits are essentially instant, so minimal load time
      const loadTime = Date.now() - startTime;
      metrics.totalLoadTime += loadTime;
      metrics.averageLoadTime = metrics.totalLoadTime / metrics.totalRequests;
    }
  }

  private recordCacheMiss(cacheKey: string): void {
    const metrics = this.performanceMetrics[cacheKey];
    if (metrics) {
      metrics.totalRequests++;
      metrics.cacheMisses++;
      metrics.cacheHitRate = (metrics.cacheHits / metrics.totalRequests) * 100;
    }
  }

  private recordLoadCompletion(cacheKey: string, startTime: number): void {
    const metrics = this.performanceMetrics[cacheKey];
    if (metrics) {
      const loadTime = Date.now() - startTime;
      metrics.totalLoadTime += loadTime;
      metrics.averageLoadTime = metrics.totalLoadTime / metrics.totalRequests;
    }
  }
}
