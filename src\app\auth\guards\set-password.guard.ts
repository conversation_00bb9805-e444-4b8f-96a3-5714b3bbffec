import { CanActivateFn ,Router} from '@angular/router';
import{inject} from '@angular/core';
import { NavigationService } from '../../services/navigation.service';

export const setPasswordGuard: CanActivateFn = (route, state) => {
 const navigationService = inject(NavigationService);

  const canAccessSetPassword = localStorage.getItem('canAccessSetPassword');

  if (canAccessSetPassword === 'true') {
    // Allow access and remove the flag after use
    localStorage.removeItem('canAccessSetPassword');
    return true;
  } else {
    // Redirect to login if the flag is not set
    navigationService.navigateToLogin();
    return false;
  }
};
