import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TableUtilsService {

  constructor() { }

  /**
   * Extracts the table API ID from a table name by taking the part before the comma
   * @param tableName The table name that may contain a comma
   * @returns The table API ID (part before comma) or the original name if no comma
   */
  extractTablesApiId(tableName?: string): string {
    if (!tableName) {
      return '';
    }
    
    if (tableName.includes(',')) {
      return tableName.split(',')[0].trim();
    }
    return tableName;
  }
} 