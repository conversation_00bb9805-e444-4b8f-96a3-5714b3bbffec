import { HttpInterceptorFn } from '@angular/common/http';

export const privilegesInterceptor: HttpInterceptorFn = (req, next) => {
  // Read privileges from sessionStorage
  const privilegesStr = sessionStorage.getItem('selectedBranch');
  let firstPrivilege = null;

  if (privilegesStr) {
    try {
      const privilegesArr = JSON.parse(privilegesStr);
      
      if (Array.isArray(privilegesArr) && privilegesArr.length > 0) {
        firstPrivilege = JSON.stringify(privilegesArr[0]);
      }
    } catch (e) {
      // Handle JSON parse error if needed
    }
  }

  const headers: any = {};
  if (privilegesStr) {
    headers['privileges'] = privilegesStr;
  }

  const modifiedReq = req.clone({
    setHeaders: headers
  });

  return next(modifiedReq);
};
